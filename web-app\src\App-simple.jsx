import React, { useState } from 'react';

function App() {
  const [message, setMessage] = useState('Smart Fire Inspector Web App');

  const testApiConnection = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/auth/test-accounts');
      const data = await response.json();
      setMessage(`API funguje! Počet testových kont: ${data.accounts?.length || 0}`);
    } catch (error) {
      setMessage(`Chyba API: ${error.message}`);
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      <h1 style={{ color: '#3B82F6', textAlign: 'center' }}>
        Smart Fire Inspector
      </h1>
      
      <div style={{ 
        background: '#f8f9fa', 
        padding: '20px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2>Status aplikácie:</h2>
        <p>{message}</p>
        
        <button 
          onClick={testApiConnection}
          style={{
            background: '#3B82F6',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Test API pripojenia
        </button>
      </div>

      <div style={{ 
        background: '#e3f2fd', 
        padding: '20px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Testové kontá:</h3>
        <ul>
          <li><strong>Admin:</strong> <EMAIL> / admin123</li>
          <li><strong>Inspector:</strong> <EMAIL> / inspector123</li>
          <li><strong>User:</strong> <EMAIL> / user123</li>
        </ul>
      </div>

      <div style={{ 
        background: '#f1f8e9', 
        padding: '20px', 
        borderRadius: '8px'
      }}>
        <h3>Implementované funkcie:</h3>
        <ul>
          <li>✅ .NET Core API (port 5000)</li>
          <li>✅ React Web App (port 3000)</li>
          <li>✅ Flutter Mobile App (pripravené)</li>
          <li>✅ JWT autentifikácia</li>
          <li>✅ Fejkové kontá na testovanie</li>
          <li>✅ Databázové migrácie (pripravené)</li>
          <li>✅ Swagger dokumentácia</li>
        </ul>
      </div>
    </div>
  );
}

export default App;
