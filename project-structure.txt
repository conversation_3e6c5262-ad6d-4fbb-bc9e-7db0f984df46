/my-project
  /mobile-app         # Flutter application
    /lib
      /main.dart      # Entry point
      /screens        # UI screens
      /services
        /api_service.dart    # Komunikácia s .NET API
      /models         # Data models
      /utils          # Pomocné funkcie
    /pubspec.yaml     # Dependencies (http, shared_preferences)
    
  /web-app            # React SPA
    /src
      /components     # UI components
      /services
        /api.ts       # Komunikácia s .NET API
      /hooks          # Custom React hooks
      /pages          # Page components
      /utils          # Pomocné funkcie
    /package.json     # Dependencies (axios)
    /vite.config.ts   # Vite configuration
    /tailwind.config.js # Tailwind configuration
    
  /backend            # .NET Core Web API
    /Controllers      # API endpoints pre všetky operácie
    /Services         # Business logika
      /AuthService.cs # Autentifikácia cez Supabase
      /StorageService.cs # Práca so Supabase Storage
      /DataService.cs # Dátové operácie
    /Models           # Data models
    /Repositories     # Data access s Dapper
    /Integrations
      /Supabase
        /SupabaseAuthClient.cs  # Klient pre Supabase Auth
        /SupabaseStorageClient.cs # Klient pre Supabase Storage
        /SupabaseConfig.cs      # Konfigurácia Supabase
    /Middleware       # API middleware (auth, logging)
    /Config           # Aplikačná konfigurácia
    
  /database           # Database scripts
    /migrations       # SQL migration scripts
    /seed-data        # Initial data
    /queries          # Uložené SQL dotazy

