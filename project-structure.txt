/SmartFireInspector
  /mobile-app         # Flutter application
    /lib
      /main.dart      # Entry point
      /screens        # UI screens
        /login_screen.dart    # Prihlásenie
        /dashboard_screen.dart # Hlav<PERSON> obrazovka
        /inspection_screen.dart # Kontrola zariadení
      /services
        /api_service.dart     # Komunikácia s .NET API
        /auth_service.dart    # Lokálna správa autentifikácie
      /models         # Data models
        /user_model.dart      # Model používateľa
        /inspection_model.dart # Model kontroly
      /utils          # Pomocné funkcie
        /storage_utils.dart   # Lokálne úložisko
    /pubspec.yaml     # Dependencies (http, shared_preferences, flutter_secure_storage)

  /web-app            # React SPA
    /src
      /components     # UI components
        /Login        # Prihlásenie komponenty
        /Dashboard    # Dashboard komponenty
        /Inspections  # Kontrola komponenty
      /services
        /api.ts       # Komunikácia s .NET API
        /auth.ts      # Autentifikačné služby
      /hooks          # Custom React hooks
        /useAuth.ts   # Hook pre autentifikáciu
      /pages          # Page components
        /LoginPage.tsx
        /DashboardPage.tsx
        /InspectionsPage.tsx
      /utils          # Pomocné funkcie
        /storage.ts   # Local/Session storage
      /contexts       # React contexts
        /AuthContext.tsx # Kontext pre autentifikáciu
    /package.json     # Dependencies (axios, react-router-dom)
    /vite.config.ts   # Vite configuration
    /tailwind.config.js # Tailwind configuration

  /backend            # .NET Core Web API
    /Controllers      # API endpoints pre všetky operácie
      /AuthController.cs    # Autentifikácia a registrácia
      /UsersController.cs   # Správa používateľov
      /InspectionsController.cs # Kontroly zariadení
    /Services         # Business logika
      /AuthService.cs       # Vlastná autentifikácia (JWT)
      /UserService.cs       # Správa používateľov
      /PasswordService.cs   # Hashovanie hesiel (BCrypt)
      /StorageService.cs    # Práca so Supabase Storage
      /DataService.cs       # Dátové operácie
      /JwtService.cs        # JWT token management
    /Models           # Data models
      /User.cs              # Model používateľa
      /LoginRequest.cs      # Login request model
      /RegisterRequest.cs   # Registration request model
      /AuthResponse.cs      # Auth response model
      /Inspection.cs        # Model kontroly
    /Repositories     # Data access s Dapper
      /UserRepository.cs    # Repozitár pre používateľov
      /InspectionRepository.cs # Repozitár pre kontroly
    /Integrations
      /Supabase
        /SupabaseStorageClient.cs # Klient pre Supabase Storage (len storage)
        /SupabaseConfig.cs        # Konfigurácia Supabase
    /Middleware       # API middleware
      /JwtMiddleware.cs     # JWT autentifikácia middleware
      /ErrorHandlingMiddleware.cs # Error handling
    /Config           # Aplikačná konfigurácia
      /JwtSettings.cs       # JWT konfigurácia

  /database           # Database scripts
    /migrations       # SQL migration scripts
      /001_create_users_table.sql    # Tabuľka používateľov
      /002_create_inspections_table.sql # Tabuľka kontrol
      /003_create_devices_table.sql     # Tabuľka zariadení
    /seed-data        # Initial data
      /default_admin_user.sql # Predvolený admin používateľ
    /queries          # Uložené SQL dotazy
      /user_queries.sql     # Dotazy pre používateľov
    /schema           # Database schema
      /database_design.sql  # Kompletná schéma DB

