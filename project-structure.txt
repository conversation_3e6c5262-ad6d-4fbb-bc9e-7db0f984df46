/SmartFireInspector
  /mobile-app         # Flutter aplikácia
    /lib
      /main.dart      # Entry point
      /screens        # UI screens
        /login_screen.dart      # Prihlásenie
        /dashboard_screen.dart  # Hlavná obrazovka
        /inspection_screen.dart # Kontrola zariadení
        /device_detail_screen.dart # Detail zariadenia
        /profile_screen.dart    # Profil používateľa
      /services
        /api_service.dart       # Komunikácia s .NET Core API
        /auth_service.dart      # Lokálna správa autentifikácie (JWT)
        /image_service.dart     # Upload obrázkov cez API
      /models         # Data models
        /user_model.dart        # Model používateľa
        /inspection_model.dart  # Model kontroly
        /device_model.dart      # Model zariadenia
      /utils          # Pomocné funkcie
        /storage_utils.dart     # Lokálne úložisko (secure storage)
        /constants.dart         # Konštanty aplikácie
      /widgets        # Reusable widgets
        /custom_button.dart
        /loading_widget.dart
    /pubspec.yaml     # Dependencies (http, shared_preferences, flutter_secure_storage, image_picker)
    /android          # Android konfigurácia
    /ios              # iOS konfigurácia

  /web-app            # Čistý React SPA (bez Next.js)
    /public           # Statické súbory
      /index.html
      /favicon.ico
    /src
      /components     # UI komponenty
        /common       # Spoločné komponenty
          /Header.jsx
          /Sidebar.jsx
          /LoadingSpinner.jsx
        /auth         # Autentifikačné komponenty
          /LoginForm.jsx
          /ProtectedRoute.jsx
        /dashboard    # Dashboard komponenty
          /DashboardStats.jsx
          /RecentInspections.jsx
        /inspections  # Kontrola komponenty
          /InspectionList.jsx
          /InspectionForm.jsx
          /ImageUpload.jsx
      /services
        /api.js       # Komunikácia s .NET Core API (axios)
        /auth.js      # Autentifikačné služby (JWT handling)
        /storage.js   # LocalStorage/SessionStorage utils
      /hooks          # Custom React hooks
        /useAuth.js   # Hook pre autentifikáciu
        /useApi.js    # Hook pre API calls
      /pages          # Page komponenty
        /LoginPage.jsx
        /DashboardPage.jsx
        /InspectionsPage.jsx
        /DevicesPage.jsx
        /ProfilePage.jsx
      /utils          # Pomocné funkcie
        /constants.js # Konštanty aplikácie
        /helpers.js   # Helper funkcie
      /contexts       # React contexts
        /AuthContext.jsx # Kontext pre autentifikáciu
      /styles         # CSS súbory
        /index.css
        /components.css
    /package.json     # Dependencies (react, react-dom, react-router-dom, axios)
    /vite.config.js   # Vite configuration
    /tailwind.config.js # Tailwind CSS configuration

  /backend            # .NET Core Web API
    /Controllers      # API endpoints
      /AuthController.cs        # Autentifikácia a registrácia
      /UsersController.cs       # Správa používateľov
      /InspectionsController.cs # CRUD operácie pre kontroly
      /DevicesController.cs     # CRUD operácie pre zariadenia
      /StorageController.cs     # Upload/download obrázkov cez Supabase Storage
    /Services         # Business logika
      /AuthService.cs           # Vlastná autentifikácia (JWT)
      /UserService.cs           # Správa používateľov
      /PasswordService.cs       # Hashovanie hesiel (BCrypt)
      /InspectionService.cs     # Business logika pre kontroly
      /DeviceService.cs         # Business logika pre zariadenia
      /StorageService.cs        # Práca so Supabase Storage API
      /JwtService.cs            # JWT token management
    /Models           # Data models a DTOs
      /Entities         # Database entity models
        /User.cs                # Entita používateľa
        /Inspection.cs          # Entita kontroly
        /Device.cs              # Entita zariadenia
        /InspectionImage.cs     # Entita obrázka kontroly
      /DTOs             # Data Transfer Objects
        /Auth
          /LoginRequest.cs      # Login request DTO
          /RegisterRequest.cs   # Registration request DTO
          /AuthResponse.cs      # Auth response DTO
        /Inspections
          /InspectionDto.cs     # Inspection DTO
          /CreateInspectionDto.cs
          /UpdateInspectionDto.cs
        /Devices
          /DeviceDto.cs         # Device DTO
    /Repositories     # Data access layer (Dapper + Supabase PostgreSQL)
      /Interfaces       # Repository interfaces
        /IUserRepository.cs
        /IInspectionRepository.cs
        /IDeviceRepository.cs
      /Implementations  # Repository implementations
        /UserRepository.cs      # Dapper queries pre users tabuľku
        /InspectionRepository.cs # Dapper queries pre inspections tabuľku
        /DeviceRepository.cs    # Dapper queries pre devices tabuľku
      /Base
        /BaseRepository.cs      # Základná trieda s Supabase connection
    /Integrations
      /Supabase
        /SupabaseStorageClient.cs # Klient pre Supabase Storage API (len storage)
        /SupabaseDbClient.cs      # Klient pre Supabase PostgreSQL (cez Dapper)
        /SupabaseConfig.cs        # Konfigurácia Supabase (DB + Storage)
    /Middleware       # API middleware
      /JwtMiddleware.cs         # JWT autentifikácia middleware
      /ErrorHandlingMiddleware.cs # Global error handling
      /LoggingMiddleware.cs     # Request/response logging
    /Config           # Aplikačná konfigurácia
      /JwtSettings.cs           # JWT konfigurácia
      /SupabaseSettings.cs      # Supabase connection settings
      /AppSettings.cs           # Všeobecné nastavenia aplikácie
    /Extensions       # Extension methods
      /ServiceCollectionExtensions.cs # DI registrácie
    /Program.cs       # Entry point
    /appsettings.json # Konfiguračný súbor

  /database           # Database scripts (Supabase PostgreSQL)
    /migrations       # SQL migration scripts
      /001_create_users_table.sql       # Tabuľka používateľov (vlastná auth)
      /002_create_devices_table.sql     # Tabuľka zariadení
      /003_create_inspections_table.sql # Tabuľka kontrol
      /004_create_inspection_images_table.sql # Tabuľka obrázkov kontrol
      /005_create_indexes.sql           # Indexy pre výkon
    /seed-data        # Počiatočné dáta
      /001_default_admin_user.sql       # Predvolený admin používateľ
      /002_sample_devices.sql           # Vzorové zariadenia
    /queries          # Uložené SQL dotazy pre Dapper
      /user_queries.sql                 # Dotazy pre používateľov
      /inspection_queries.sql           # Dotazy pre kontroly
      /device_queries.sql               # Dotazy pre zariadenia
    /schema           # Database schema dokumentácia
      /database_design.sql              # Kompletná schéma DB
      /relationships.md                 # Dokumentácia vzťahov medzi tabuľkami

  /docs               # Projektová dokumentácia
    /api              # API dokumentácia
      /endpoints.md   # Zoznam všetkých API endpoints
      /authentication.md # Dokumentácia autentifikácie
    /database         # Databázová dokumentácia
      /schema.md      # Popis databázovej schémy
    /deployment       # Deployment dokumentácia
      /setup.md       # Návod na nastavenie projektu

  /tests              # Testy (voliteľné)
    /backend-tests    # Unit testy pre .NET Core API
    /integration-tests # Integračné testy

  /.env.example       # Príklad environment variables
  /README.md          # Hlavná dokumentácia projektu
  /.gitignore         # Git ignore súbor

