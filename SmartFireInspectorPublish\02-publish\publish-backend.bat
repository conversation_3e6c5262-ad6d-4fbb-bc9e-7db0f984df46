@echo off
echo ========================================
echo Smart Fire Inspector - Backend API Publish
echo ========================================
echo.

set SOURCE_DIR=c:\SmartFireInspector\backend
set PUBLISH_DIR=c:\SmartFireInspectorPublish\published\backend-api

echo Kontrolujem zdrojovy adresar...
if not exist "%SOURCE_DIR%" (
    echo CHYBA: Zdrojovy adresar neexistuje: %SOURCE_DIR%
    pause
    exit /b 1
)

echo Vytvariam publish adresar...
if exist "%PUBLISH_DIR%" (
    echo Mazem stary publish adresar...
    rmdir /s /q "%PUBLISH_DIR%"
)
mkdir "%PUBLISH_DIR%" 2>nul

echo.
echo Publikujem .NET API...
cd /d "%SOURCE_DIR%"

echo Spustam dotnet clean...
dotnet clean

echo Spustam dotnet restore...
dotnet restore

echo Spustam dotnet publish...
dotnet publish -c Release -o "%PUBLISH_DIR%" --self-contained false

if %ERRORLEVEL% neq 0 (
    echo.
    echo CHYBA: Publish zlyhal!
    pause
    exit /b 1
)

echo.
echo Vytvariam spustaci skript...
echo @echo off > "%PUBLISH_DIR%\start-api.bat"
echo echo Smart Fire Inspector API Server >> "%PUBLISH_DIR%\start-api.bat"
echo echo ================================= >> "%PUBLISH_DIR%\start-api.bat"
echo echo. >> "%PUBLISH_DIR%\start-api.bat"
echo echo Spustam API server na http://localhost:5000 >> "%PUBLISH_DIR%\start-api.bat"
echo echo Pre ukoncenie stlacte Ctrl+C >> "%PUBLISH_DIR%\start-api.bat"
echo echo. >> "%PUBLISH_DIR%\start-api.bat"
echo SmartFireInspector.Api.exe --urls=http://0.0.0.0:5000 >> "%PUBLISH_DIR%\start-api.bat"
echo pause >> "%PUBLISH_DIR%\start-api.bat"

echo.
echo Vytvariam README.txt...
echo Smart Fire Inspector - Backend API > "%PUBLISH_DIR%\README.txt"
echo ================================== >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Build datum: %date% %time% >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Pre spustenie API servera: >> "%PUBLISH_DIR%\README.txt"
echo 1. Spustite start-api.bat >> "%PUBLISH_DIR%\README.txt"
echo 2. API bude dostupne na http://localhost:5000 >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Poznamky: >> "%PUBLISH_DIR%\README.txt"
echo - Potrebujete .NET 8 Runtime >> "%PUBLISH_DIR%\README.txt"
echo - Nakonfigurujte databazove pripojenie v appsettings.json >> "%PUBLISH_DIR%\README.txt"
echo - Pre produkciu zmente connection string >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Testovanie: >> "%PUBLISH_DIR%\README.txt"
echo - Test accounts: http://localhost:5000/api/auth/test-accounts >> "%PUBLISH_DIR%\README.txt"
echo - Swagger UI: http://localhost:5000/swagger (ak je povolene) >> "%PUBLISH_DIR%\README.txt"

echo.
echo Kontrolujem velkost publikovanych suborov...
dir "%PUBLISH_DIR%" /s /-c | find "File(s)"

echo.
echo ========================================
echo USPECH: Backend API publikovany!
echo ========================================
echo Umiestnenie: %PUBLISH_DIR%
echo Pre spustenie: %PUBLISH_DIR%\start-api.bat
echo.
pause
