# API Endpoints

## Autentifikácia

### POST /api/auth/login
Prihlásenie používateľa

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Prihlásenie úspešné",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "<PERSON>už<PERSON>vateľ",
    "role": "Admin"
  }
}
```

### GET /api/auth/test-accounts
Zoznam testových kont (len pre development)

**Response:**
```json
{
  "message": "Testové kontá pre prihlásenie:",
  "accounts": [
    {
      "email": "<EMAIL>",
      "password": "admin123",
      "role": "Admin"
    }
  ]
}
```

## Používatelia

### GET /api/users/profile
Získanie profilu aktuálneho používateľa

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "Admin",
  "lastName": "Používateľ",
  "role": "Admin"
}
```

## Plánované endpoints

### Zariadenia
- `GET /api/devices` - Zoznam zariadení
- `GET /api/devices/{id}` - Detail zariadenia
- `POST /api/devices` - Vytvorenie zariadenia
- `PUT /api/devices/{id}` - Aktualizácia zariadenia
- `DELETE /api/devices/{id}` - Zmazanie zariadenia

### Kontroly
- `GET /api/inspections` - Zoznam kontrol
- `GET /api/inspections/{id}` - Detail kontroly
- `POST /api/inspections` - Vytvorenie kontroly
- `PUT /api/inspections/{id}` - Aktualizácia kontroly
- `DELETE /api/inspections/{id}` - Zmazanie kontroly

### Storage
- `POST /api/storage/upload` - Upload obrázka
- `GET /api/storage/{filename}` - Stiahnutie obrázka
- `DELETE /api/storage/{filename}` - Zmazanie obrázka
