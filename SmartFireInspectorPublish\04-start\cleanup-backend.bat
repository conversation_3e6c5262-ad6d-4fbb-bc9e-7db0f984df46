@echo off
echo ========================================
echo Smart Fire Inspector - Backend Cleanup
echo ========================================
echo.

echo Tento skript vycisti vsetky beziacich backend procesy a build cache.
echo.

set BACKEND_DIR=c:\SmartFireInspector\backend

echo [1/4] Ukoncujem vsetky .NET procesy...
echo.

REM Ukoncenie SmartFireInspector.Api procesov
tasklist /FI "IMAGENAME eq SmartFireInspector.Api.exe" 2>NUL | find /I /N "SmartFireInspector.Api.exe" > NUL
if "%ERRORLEVEL%"=="0" (
    echo Ukoncujem SmartFireInspector.Api.exe...
    taskkill /F /IM SmartFireInspector.Api.exe /T
    timeout /t 2 /nobreak > NUL
) else (
    echo SmartFireInspector.Api.exe nie je spusteny.
)

REM Ukoncenie dotnet procesov
tasklist /FI "IMAGENAME eq dotnet.exe" 2>NUL | find /I /N "dotnet.exe" > NUL
if "%ERRORLEVEL%"=="0" (
    echo Ukoncujem dotnet.exe procesy...
    taskkill /F /IM dotnet.exe /T
    timeout /t 2 /nobreak > NUL
) else (
    echo Ziadne dotnet.exe procesy nie su spustene.
)

echo.
echo [2/4] Uvolnujem porty...
echo.

REM Kontrola a uvolnenie portu 5000
netstat -ano | findstr :5000 > port_check.tmp 2>NUL
if exist port_check.tmp (
    echo Port 5000 je obsadeny, uvolnujem...
    for /f "tokens=5" %%a in (port_check.tmp) do (
        echo Ukoncujem proces PID: %%a
        taskkill /F /PID %%a > NUL 2>&1
    )
    del port_check.tmp
    timeout /t 2 /nobreak > NUL
    echo Port 5000 uvolneny.
) else (
    echo Port 5000 je volny.
)

REM Kontrola a uvolnenie portu 5001 (HTTPS)
netstat -ano | findstr :5001 > port_check.tmp 2>NUL
if exist port_check.tmp (
    echo Port 5001 je obsadeny, uvolnujem...
    for /f "tokens=5" %%a in (port_check.tmp) do (
        echo Ukoncujem proces PID: %%a
        taskkill /F /PID %%a > NUL 2>&1
    )
    del port_check.tmp
    timeout /t 2 /nobreak > NUL
    echo Port 5001 uvolneny.
) else (
    echo Port 5001 je volny.
)

echo.
echo [3/4] Cistenie build cache...
echo.

if exist "%BACKEND_DIR%" (
    cd /d "%BACKEND_DIR%"
    
    if exist "bin" (
        echo Mazem bin adresar...
        rmdir /s /q bin 2>NUL
        if exist "bin" (
            echo POZOR: bin adresar sa nepodarilo vymazat uplne.
        ) else (
            echo bin adresar vymazany.
        )
    ) else (
        echo bin adresar neexistuje.
    )
    
    if exist "obj" (
        echo Mazem obj adresar...
        rmdir /s /q obj 2>NUL
        if exist "obj" (
            echo POZOR: obj adresar sa nepodarilo vymazat uplne.
        ) else (
            echo obj adresar vymazany.
        )
    ) else (
        echo obj adresar neexistuje.
    )
) else (
    echo CHYBA: Backend adresar neexistuje: %BACKEND_DIR%
)

echo.
echo [4/4] Cistenie .NET cache...
echo.

echo Cistim NuGet cache...
dotnet nuget locals all --clear > NUL 2>&1
if %ERRORLEVEL% equ 0 (
    echo NuGet cache vymazany.
) else (
    echo POZOR: NuGet cache sa nepodarilo vymazat.
)

echo.
echo ========================================
echo CLEANUP DOKONCENY!
echo ========================================
echo.

echo Vsetky backend procesy su ukoncene.
echo Build cache je vymazany.
echo Porty 5000 a 5001 su uvolnene.
echo.

echo Teraz mozete bezpecne spustit:
echo start-backend-dev.bat
echo.

echo Stlacte lubovolnu klavesu...
pause > nul
