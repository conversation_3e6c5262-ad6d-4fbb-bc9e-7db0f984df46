@echo off
echo ========================================
echo Test Backend Working - Smart Fire Inspector
echo ========================================
echo.

echo Testujem, ci backend API bezi na porte 5000...
echo.

echo [1] Kontrolujem port 5000...
netstat -ano | findstr :5000 > port_check.tmp
if exist port_check.tmp (
    echo [OK] Port 5000 je obsadeny - backend pravdepodobne bezi
    type port_check.tmp
    del port_check.tmp
) else (
    echo [INFO] Port 5000 nie je obsadeny - backend nebezi
)

echo.
echo [2] Testujem HTTP pripojenie na localhost:5000...

REM Test zakladneho endpointu
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000' -UseBasicParsing -TimeoutSec 5; Write-Host '[OK] Backend odpovedal na http://localhost:5000'; Write-Host 'Status:' $response.StatusCode } catch { Write-Host '[ERROR] Backend neodpovedal:' $_.Exception.Message }"

echo.
echo [3] Testujem API endpoint /api/auth/test-accounts...

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth/test-accounts' -UseBasicParsing -TimeoutSec 5; Write-Host '[OK] API endpoint funguje!'; Write-Host 'Status:' $response.StatusCode; Write-Host 'Response:'; Write-Host $response.Content } catch { Write-Host '[ERROR] API endpoint nefunguje:' $_.Exception.Message }"

echo.
echo [4] Testujem Swagger UI...

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/swagger' -UseBasicParsing -TimeoutSec 5; Write-Host '[OK] Swagger UI je dostupny'; Write-Host 'Status:' $response.StatusCode } catch { Write-Host '[ERROR] Swagger UI nie je dostupny:' $_.Exception.Message }"

echo.
echo [5] Kontrolujem procesy...

echo Backend procesy:
tasklist /FI "IMAGENAME eq SmartFireInspector.Api.exe" 2>NUL
tasklist /FI "IMAGENAME eq dotnet.exe" 2>NUL | findstr SmartFireInspector 2>NUL

echo.
echo ========================================
echo TEST DOKONCENY!
echo ========================================
echo.

echo SUHRN:
echo - Ak vidite [OK] spravy, backend funguje spravne
echo - Ak vidite [ERROR] spravy, backend ma problemy
echo.

echo DOSTUPNE ENDPOINTY:
echo - http://localhost:5000 (zakladny)
echo - http://localhost:5000/api/auth/test-accounts (test API)
echo - http://localhost:5000/swagger (Swagger UI)
echo.

echo Pre spustenie backendu pouzite:
echo start-backend-dev.bat
echo.

echo Stlacte lubovolnu klavesu...
pause > nul
