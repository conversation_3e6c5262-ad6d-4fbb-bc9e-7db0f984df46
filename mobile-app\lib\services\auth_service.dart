import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'api_service.dart';
import 'storage_service.dart';
import 'offline_service.dart';
import 'connectivity_service.dart';

class AuthService extends ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _error;
  bool _isOfflineMode = false;
  final ConnectivityService _connectivityService;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;
  bool get isOfflineMode => _isOfflineMode;

  AuthService(this._connectivityService) {
    _loadUserFromStorage();
    _connectivityService.addListener(_onConnectivityChanged);
  }

  Future<void> _loadUserFromStorage() async {
    _setLoading(true);
    try {
      final user = await StorageService.getUser();
      final isAuth = await StorageService.isAuthenticated();
      
      if (user != null && isAuth) {
        _user = user;
      }
    } catch (e) {
      _setError('Failed to load user data');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // Check connectivity
      final isOnline = await _connectivityService.checkConnectivity();

      if (isOnline) {
        // Try online login first
        try {
          final response = await ApiService.login(email, password);

          if (response.success && response.user != null && response.token != null) {
            _user = response.user;
            _isOfflineMode = false;

            await StorageService.setToken(response.token!);
            await StorageService.setUser(response.user!);

            // Save user for offline access
            await OfflineService.saveOfflineUser(response.user!, password);
            await OfflineService.setLastSyncTime();

            notifyListeners();
            return true;
          } else {
            _setError(response.message);
            return false;
          }
        } catch (e) {
          print('Online login failed, trying offline: $e');
          return await _tryOfflineLogin(email, password);
        }
      } else {
        // Offline mode
        return await _tryOfflineLogin(email, password);
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> _tryOfflineLogin(String email, String password) async {
    try {
      final response = await OfflineService.authenticateOffline(email, password);

      if (response.success && response.user != null) {
        _user = response.user;
        _isOfflineMode = true;

        await StorageService.setToken(response.token!);
        await StorageService.setUser(response.user!);

        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Offline prihlásenie zlyhalo: $e');
      return false;
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    try {
      await StorageService.clearAuthData();
      _user = null;
      notifyListeners();
    } catch (e) {
      _setError('Failed to logout');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshProfile() async {
    if (!isAuthenticated) return;

    try {
      final user = await ApiService.getProfile();
      _user = user;
      await StorageService.setUser(user);
      notifyListeners();
    } catch (e) {
      _setError('Failed to refresh profile');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  void _onConnectivityChanged() {
    if (_connectivityService.isOnline && _isOfflineMode && _user != null) {
      print('📶 Connection restored - syncing data...');
      _syncOfflineData();
    }
  }

  Future<void> _syncOfflineData() async {
    try {
      // Sync any pending offline data
      final unsyncedKeys = await OfflineService.getUnsyncedDataKeys();
      print('🔄 Syncing ${unsyncedKeys.length} offline items...');

      for (final key in unsyncedKeys) {
        // Mark as synced (implement actual sync logic later)
        await OfflineService.markDataAsSynced(key);
      }

      await OfflineService.setLastSyncTime();
      print('✅ Offline data synced successfully');
    } catch (e) {
      print('❌ Sync failed: $e');
    }
  }

  Future<List<String>> getOfflineUsers() async {
    return await OfflineService.getOfflineUserEmails();
  }

  Future<bool> hasOfflineAccess(String email) async {
    return await OfflineService.hasOfflineAccess(email);
  }

  Future<DateTime?> getLastSyncTime() async {
    return await OfflineService.getLastSyncTime();
  }

  @override
  void dispose() {
    _connectivityService.removeListener(_onConnectivityChanged);
    super.dispose();
  }
}
