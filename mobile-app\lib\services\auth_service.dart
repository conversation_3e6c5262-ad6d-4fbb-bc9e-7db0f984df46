import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'api_service.dart';
import 'storage_service.dart';

class AuthService extends ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _error;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;

  AuthService() {
    _loadUserFromStorage();
  }

  Future<void> _loadUserFromStorage() async {
    _setLoading(true);
    try {
      final user = await StorageService.getUser();
      final isAuth = await StorageService.isAuthenticated();
      
      if (user != null && isAuth) {
        _user = user;
      }
    } catch (e) {
      _setError('Failed to load user data');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.login(email, password);
      
      if (response.success && response.user != null && response.token != null) {
        _user = response.user;
        await StorageService.setToken(response.token!);
        await StorageService.setUser(response.user!);
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    try {
      await StorageService.clearAuthData();
      _user = null;
      notifyListeners();
    } catch (e) {
      _setError('Failed to logout');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshProfile() async {
    if (!isAuthenticated) return;

    try {
      final user = await ApiService.getProfile();
      _user = user;
      await StorageService.setUser(user);
      notifyListeners();
    } catch (e) {
      _setError('Failed to refresh profile');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
