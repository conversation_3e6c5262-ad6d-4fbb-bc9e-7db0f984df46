# Smart Fire Inspector

Systém pre správu kontrol požiarnych zariadení s mobilnou a webovou aplikáciou.

## Architektúra

- **Mobile App**: Flutter
- **Web App**: React (čist<PERSON> React s Vite)
- **Backend API**: .NET Core 8
- **Databáza**: Supabase PostgreSQL (prístup cez Dapper)
- **Storage**: Supabase Storage API
- **Autentifikácia**: Vlastná tabuľka users + JWT tokeny

## Štruktúra projektu

```
/SmartFireInspector
  /mobile-app         # Flutter aplikácia
  /web-app            # React SPA
  /backend            # .NET Core Web API
  /database           # SQL skripty a migrácie
  /docs               # Dokumentácia
```

## Spustenie projektu

### 1. Backend (.NET Core API)

```bash
cd backend
dotnet restore
dotnet run
```

API bude dostupné na: `http://localhost:5000`
Swagger dokumentácia: `http://localhost:5000/swagger`

### 2. Web aplikácia (React)

```bash
cd web-app
npm install
npm run dev
```

Web aplikácia bude dostupná na: `http://localhost:3000`

## Testové kontá

Pre testovanie sú k dispozícii tieto kontá:

| Email | Heslo | Rola |
|-------|-------|------|
| <EMAIL> | admin123 | Admin |
| <EMAIL> | inspector123 | Inspector |
| <EMAIL> | user123 | User |

## API Endpoints

### Autentifikácia
- `POST /api/auth/login` - Prihlásenie
- `GET /api/auth/test-accounts` - Zoznam testových kont

## Funkcie

### ✅ Implementované
- **Kompletná štruktúra projektu**
- **Prihlasovacia obrazovka** (React)
- **JWT autentifikácia** s vlastnými kontami
- **Fejkové kontá** na testovanie (3 roly)
- **CORS konfigurácia** medzi portmi
- **Responsive design** (Tailwind CSS)
- **Error handling** a middleware
- **Loading states** a UX
- **Swagger dokumentácia**
- **Databázové migrácie** (pripravené)
- **Seed dáta** (pripravené)
- **API dokumentácia**

### 🚧 Plánované
- Databázové pripojenie (Supabase + Dapper)
- CRUD operácie pre zariadenia
- CRUD operácie pre kontroly
- Upload obrázkov (Supabase Storage)
- Flutter mobilná aplikácia
- Dashboard s štatistikami
- Notifikácie a reporty

## Technológie

### Backend
- .NET Core 8
- JWT Authentication
- BCrypt (hashovanie hesiel)
- Swagger/OpenAPI
- Dapper ORM (pripravené)

### Frontend (React)
- React 18
- React Router DOM
- Axios (HTTP klient)
- Tailwind CSS
- Vite (build tool)

### Databáza (pripravené)
- Supabase PostgreSQL
- Dapper ORM
- SQL migrácie

## Vývoj

### Pridanie nových funkcií
1. Backend: Vytvorte Controller → Service → Repository
2. Frontend: Vytvorte Page → Components → Services
3. Aktualizujte API endpoints v `constants.js`

### Testovanie
- Backend: Swagger UI na `https://localhost:7001/swagger`
- Frontend: Browser DevTools
- API: Postman/Insomnia

## Poznámky

- Mobilná a webová aplikácia komunikujú **LEN** s .NET API
- .NET API má **výhradný** prístup do databázy
- Supabase Auth **nie je** použitý - vlastná autentifikácia
- Supabase Storage bude použitý len pre obrázky
