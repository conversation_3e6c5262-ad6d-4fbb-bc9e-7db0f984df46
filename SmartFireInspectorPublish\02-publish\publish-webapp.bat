@echo off
echo ========================================
echo Smart Fire Inspector - Web App Publish
echo ========================================
echo.

set SOURCE_DIR=c:\SmartFireInspector\web-app
set PUBLISH_DIR=c:\SmartFireInspectorPublish\published\web-app

echo Kontrolujem zdrojovy adresar...
if not exist "%SOURCE_DIR%" (
    echo CHYBA: Zdrojovy adresar neexistuje: %SOURCE_DIR%
    pause
    exit /b 1
)

echo Vytvariam publish adresar...
if exist "%PUBLISH_DIR%" (
    echo Mazem stary publish adresar...
    rmdir /s /q "%PUBLISH_DIR%"
)
mkdir "%PUBLISH_DIR%" 2>nul

echo.
echo Publikujem React Web App...
cd /d "%SOURCE_DIR%"

echo Cistim node_modules a cache...
if exist "node_modules" rmdir /s /q "node_modules"
if exist "dist" rmdir /s /q "dist"

echo Instalam dependencies...
call npm install

if %ERRORLEVEL% neq 0 (
    echo.
    echo CHYBA: npm install zlyhal!
    pause
    exit /b 1
)

echo Spustam build...
call npm run build

if %ERRORLEVEL% neq 0 (
    echo.
    echo CHYBA: npm run build zlyhal!
    pause
    exit /b 1
)

echo Kopirujeme build files...
if exist "dist" (
    xcopy /s /e /y "dist\*" "%PUBLISH_DIR%\"
) else if exist "build" (
    xcopy /s /e /y "build\*" "%PUBLISH_DIR%\"
) else (
    echo CHYBA: Build adresar nenajdeny (dist alebo build)
    pause
    exit /b 1
)

echo.
echo Vytvariam spustaci skript pre development...
echo @echo off > "%PUBLISH_DIR%\start-dev-server.bat"
echo echo Smart Fire Inspector Web App - Development Server >> "%PUBLISH_DIR%\start-dev-server.bat"
echo echo ================================================== >> "%PUBLISH_DIR%\start-dev-server.bat"
echo echo. >> "%PUBLISH_DIR%\start-dev-server.bat"
echo echo Spustam development server na http://localhost:3000 >> "%PUBLISH_DIR%\start-dev-server.bat"
echo echo Pre ukoncenie stlacte Ctrl+C >> "%PUBLISH_DIR%\start-dev-server.bat"
echo echo. >> "%PUBLISH_DIR%\start-dev-server.bat"
echo cd /d "%SOURCE_DIR%" >> "%PUBLISH_DIR%\start-dev-server.bat"
echo npm run dev >> "%PUBLISH_DIR%\start-dev-server.bat"
echo pause >> "%PUBLISH_DIR%\start-dev-server.bat"

echo.
echo Vytvariam README.txt...
echo Smart Fire Inspector - Web Application > "%PUBLISH_DIR%\README.txt"
echo ======================================= >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Build datum: %date% %time% >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Tato zlozka obsahuje build verziu web aplikacie. >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Pre nasadenie na web server: >> "%PUBLISH_DIR%\README.txt"
echo 1. Skopirujte vsetky subory do web servera (Apache, Nginx, IIS) >> "%PUBLISH_DIR%\README.txt"
echo 2. Nastavte web server aby servoval index.html ako default >> "%PUBLISH_DIR%\README.txt"
echo 3. Nakonfigurujte API endpoint v konfiguraci >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Pre development: >> "%PUBLISH_DIR%\README.txt"
echo - Spustite start-dev-server.bat >> "%PUBLISH_DIR%\README.txt"
echo - Aplikacia bude dostupna na http://localhost:3000 >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Poznamky: >> "%PUBLISH_DIR%\README.txt"
echo - Potrebujete Node.js pre development server >> "%PUBLISH_DIR%\README.txt"
echo - Pre produkciu nakonfigurujte spravny API endpoint >> "%PUBLISH_DIR%\README.txt"

echo.
echo Kontrolujem velkost publikovanych suborov...
dir "%PUBLISH_DIR%" /s /-c | find "File(s)"

echo.
echo ========================================
echo USPECH: Web App publikovana!
echo ========================================
echo Umiestnenie: %PUBLISH_DIR%
echo Pre development: %PUBLISH_DIR%\start-dev-server.bat
echo.
pause
