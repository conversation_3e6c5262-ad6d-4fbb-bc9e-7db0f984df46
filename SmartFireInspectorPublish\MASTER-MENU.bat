@echo off
title Smart Fire Inspector - Master Control Panel
color 0A

:main_menu
cls
echo.
echo ========================================
echo   Smart Fire Inspector - Master Menu
echo ========================================
echo.
echo Vyberte akciu:
echo.
echo [1] KONFIGURACIA
echo     1. Kontrola prostredia
echo     2. Konfiguracia mobile API
echo.
echo [2] PUBLIKOVANIE
echo     3. Publikovat vsetko
echo     4. Publikovat Backend API
echo     5. Publikovat Web App
echo     6. Publikovat Mobile App
echo.
echo [3] NASADENIE
echo     7. Nasadit mobile na USB zariadenie
echo     8. Nasadit mobile do emulatora
echo.
echo [4] SPUSTENIE (DEVELOPMENT)
echo     9. Spustit vsetky sluzby
echo    10. Spustit len Backend API
echo    11. Spustit len Web App
echo    12. Spustit len Mobile App
echo.
echo [0] Ukoncit
echo.
set /p choice="Zadajte volbu (0-12): "

if "%choice%"=="0" goto :exit
if "%choice%"=="1" goto :env_check
if "%choice%"=="2" goto :mobile_config
if "%choice%"=="3" goto :publish_all
if "%choice%"=="4" goto :publish_backend
if "%choice%"=="5" goto :publish_webapp
if "%choice%"=="6" goto :publish_mobile
if "%choice%"=="7" goto :deploy_usb
if "%choice%"=="8" goto :deploy_emulator
if "%choice%"=="9" goto :start_all
if "%choice%"=="10" goto :start_backend
if "%choice%"=="11" goto :start_webapp
if "%choice%"=="12" goto :start_mobile

echo Neplatna volba! Stlacte lubovolnu klavesu...
pause > nul
goto :main_menu

:env_check
cls
echo Spustam kontrolu prostredia...
call "01-config\environment-setup.bat"
goto :main_menu

:mobile_config
cls
echo Spustam konfiguraciu mobile API...
call "01-config\mobile-api-config.bat"
goto :main_menu

:publish_all
cls
echo Spustam publikovanie vsetkych komponentov...
call "02-publish\publish-all.bat"
goto :main_menu

:publish_backend
cls
echo Spustam publikovanie Backend API...
call "02-publish\publish-backend.bat"
goto :main_menu

:publish_webapp
cls
echo Spustam publikovanie Web App...
call "02-publish\publish-webapp.bat"
goto :main_menu

:publish_mobile
cls
echo Spustam build Mobile App...
call "02-publish\build-mobile.bat"
goto :main_menu

:deploy_usb
cls
echo Spustam nasadenie na USB zariadenie...
call "03-deploy\deploy-mobile-usb.bat"
goto :main_menu

:deploy_emulator
cls
echo Spustam nasadenie do emulatora...
call "03-deploy\deploy-mobile-emulator.bat"
goto :main_menu

:start_all
cls
echo Spustam vsetky sluzby...
call "04-start\start-all-services.bat"
goto :main_menu

:start_backend
cls
echo Spustam Backend API...
call "04-start\start-backend-dev.bat"
goto :main_menu

:start_webapp
cls
echo Spustam Web App...
call "04-start\start-webapp-dev.bat"
goto :main_menu

:start_mobile
cls
echo Spustam Mobile App...
call "04-start\start-mobile-dev.bat"
goto :main_menu

:exit
cls
echo.
echo ========================================
echo   Smart Fire Inspector - Ukoncenie
echo ========================================
echo.
echo Dakujeme za pouzitie Smart Fire Inspector!
echo.
timeout /t 2 /nobreak > nul
exit
