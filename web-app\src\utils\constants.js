// API Configuration 
// Lokalny development - localhost:5000 
export const API_BASE_URL = 'http://localhost:5000/api'; 
 
// API Endpoints 
export const API_ENDPOINTS = { 
  AUTH: { 
    LOGIN: '/auth/login', 
    TEST_ACCOUNTS: '/auth/test-accounts' 
  }, 
  DEVICES: { 
    LIST: '/devices', 
    CREATE: '/devices', 
    UPDATE: '/devices', 
    DELETE: '/devices' 
  }, 
  INSPECTIONS: { 
    LIST: '/inspections', 
    CREATE: '/inspections', 
    UPDATE: '/inspections', 
    DELETE: '/inspections' 
  }, 
  REPORTS: { 
    LIST: '/reports', 
    CREATE: '/reports', 
    EXPORT: '/reports/export' 
  } 
}; 
 
// Storage Keys 
export const STORAGE_KEYS = { 
  TOKEN: 'smartfire_token', 
  USER: 'smartfire_user' 
}; 
 
// User Roles 
export const USER_ROLES = { 
  ADMIN: 'Admin', 
  INSPECTOR: 'Inspector', 
  USER: 'User' 
}; 
 
// Request Configuration 
export const REQUEST_CONFIG = { 
  TIMEOUT: 10000, 
  RETRY_ATTEMPTS: 3, 
  RETRY_DELAY: 1000 
}; 
