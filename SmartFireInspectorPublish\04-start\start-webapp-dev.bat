@echo off
echo ========================================
echo Smart Fire Inspector - Start Web App (Development)
echo ========================================
echo.

set WEBAPP_DIR=c:\SmartFireInspector\SmartFireInspectorPublish\web-app

echo Kontrolujem web app adresar...
if not exist "%WEBAPP_DIR%" (
    echo CHYBA: Web app adresar neexistuje: %WEBAPP_DIR%
    pause
    exit /b 1
)

echo Spustam Web Application v development mode...
echo.
echo Web aplikacia bude dostupna na:
echo - http://localhost:3000
echo - http://192.168.1.X:3000 (ak je povolene v konfiguraci)
echo.
echo Pre ukoncenie stlacte Ctrl+C
echo.

cd /d "%WEBAPP_DIR%"

echo Kontrolujem dependencies...
if not exist "node_modules" (
    echo Instalam dependencies...
    call npm install
)

echo Spustam development server...
call npm run dev

pause
