# Smart Fire Inspector - Flutter Mobile App

Mobilná aplikácia pre správu kontrol požiarnych zariadení.

## Funkcie

- ✅ **Prihlasovanie** s JWT autentifikáciou
- ✅ **Dashboard** s informáciami o používateľovi
- ✅ **Testové kontá** na rýchle prihlásenie
- ✅ **Secure storage** pre tokeny
- ✅ **API komunikácia** s .NET Core backendom
- ✅ **Material Design** UI
- 🚧 **Kontroly zariadení** (pripravuje sa)
- 🚧 **Fotenie zariadení** (pripravuje sa)
- 🚧 **Offline režim** (pripravuje sa)

## Spustenie

### Požiadavky
- Flutter SDK 3.0+
- Android Studio / VS Code
- Android emulator alebo fyzické zariadenie

### Inštalácia
```bash
cd mobile-app
flutter pub get
flutter run
```

### Pre Android emulator
API endpoint je nastavený na `http://********:5000/api` (Android emulator localhost)

### Pre fyzické zariadenie
Zmeňte v `lib/utils/constants.dart`:
```dart
static const String apiBaseUrl = 'http://YOUR_IP:5000/api';
```

## Testové kontá

- **Admin**: <EMAIL> / admin123
- **Inspector**: <EMAIL> / inspector123
- **User**: <EMAIL> / user123

## Architektúra

```
Flutter App → .NET Core API (port 5000) → Supabase DB
```

### Štruktúra projektu
```
lib/
├── main.dart              # Entry point
├── models/                # Data models
│   └── user_model.dart
├── services/              # API a business logika
│   ├── api_service.dart
│   ├── auth_service.dart
│   └── storage_service.dart
├── screens/               # UI obrazovky
│   ├── login_screen.dart
│   └── dashboard_screen.dart
├── widgets/               # Reusable komponenty
│   ├── custom_button.dart
│   └── loading_widget.dart
└── utils/                 # Konštanty a helpers
    └── constants.dart
```

## Technológie

- **Flutter 3.0+**
- **Provider** (state management)
- **HTTP** (API komunikácia)
- **Flutter Secure Storage** (tokeny)
- **Shared Preferences** (používateľské dáta)
- **Image Picker** (fotenie - pripravené)
- **Go Router** (navigácia)

## Vývoj

### Pridanie nových obrazoviek
1. Vytvorte súbor v `lib/screens/`
2. Pridajte route do `main.dart`
3. Aktualizujte navigáciu

### API komunikácia
Všetky API volania idú cez `ApiService` class:
```dart
final response = await ApiService.login(email, password);
```

### State management
Používa sa Provider pattern:
```dart
Consumer<AuthService>(
  builder: (context, authService, child) {
    return Text(authService.user?.name ?? 'Guest');
  },
)
```

## Poznámky

- Aplikácia komunikuje **LEN** s .NET Core API
- **Žiadny priamy** prístup do databázy
- JWT tokeny sú uložené v secure storage
- Podporuje Android a iOS
- Material Design 3 komponenty
