@echo off
echo ========================================
echo Smart Fire Inspector - Test Ciest
echo ========================================
echo.

echo Testujem opravene cesty v skriptoch...
echo.

echo Aktualna cesta: %CD%
echo.

echo [1] Kontrolujem zdrojove adresare:
echo.

set BACKEND_DIR=c:\SmartFireInspector\SmartFireInspectorPublish\backend
set WEBAPP_DIR=c:\SmartFireInspector\SmartFireInspectorPublish\web-app
set MOBILE_DIR=c:\SmartFireInspector\SmartFireInspectorPublish\mobile-app

if exist "%BACKEND_DIR%" (
    echo [OK] Backend: %BACKEND_DIR%
) else (
    echo [CHYBA] Backend neexistuje: %BACKEND_DIR%
)

if exist "%WEBAPP_DIR%" (
    echo [OK] Web App: %WEBAPP_DIR%
) else (
    echo [CHYBA] Web App neexistuje: %WEBAPP_DIR%
)

if exist "%MOBILE_DIR%" (
    echo [OK] Mobile App: %MOBILE_DIR%
) else (
    echo [CHYBA] Mobile App neexistuje: %MOBILE_DIR%
)

echo.
echo [2] Kontrolujem publish skripty:
echo.

if exist "02-publish\publish-backend.bat" (
    echo [OK] publish-backend.bat existuje
) else (
    echo [CHYBA] publish-backend.bat neexistuje
)

if exist "02-publish\publish-webapp.bat" (
    echo [OK] publish-webapp.bat existuje
) else (
    echo [CHYBA] publish-webapp.bat neexistuje
)

if exist "02-publish\build-mobile.bat" (
    echo [OK] build-mobile.bat existuje
) else (
    echo [CHYBA] build-mobile.bat neexistuje
)

echo.
echo [3] Kontrolujem start skripty:
echo.

if exist "04-start\start-backend-dev.bat" (
    echo [OK] start-backend-dev.bat existuje
) else (
    echo [CHYBA] start-backend-dev.bat neexistuje
)

if exist "04-start\start-webapp-dev.bat" (
    echo [OK] start-webapp-dev.bat existuje
) else (
    echo [CHYBA] start-webapp-dev.bat neexistuje
)

if exist "04-start\start-mobile-dev.bat" (
    echo [OK] start-mobile-dev.bat existuje
) else (
    echo [CHYBA] start-mobile-dev.bat neexistuje
)

echo.
echo [4] Test spustenia jednoducheho skriptu:
echo.

echo Vytvariam testovaci skript...
echo @echo off > test-cesta.bat
echo echo Test cesty funguje! >> test-cesta.bat
echo echo Aktualna cesta: %%CD%% >> test-cesta.bat
echo echo Datum: %%date%% %%time%% >> test-cesta.bat

echo Spustam testovaci skript...
call test-cesta.bat

echo.
echo Mazem testovaci skript...
del test-cesta.bat

echo.
echo ========================================
echo TEST CIEST DOKONCENY!
echo ========================================
echo.

echo SUHRN:
echo - Vsetky cesty su opravene na c:\SmartFireInspector\SmartFireInspectorPublish\
echo - Skripty by mali teraz fungovat spravne
echo - Ak sa stale vyskytuju problemy, skontrolujte existenciu zdrojovych projektov
echo.

echo Pre spustenie master menu pouzite:
echo START-HERE.bat
echo.

echo Stlacte lubovolnu klavesu...
pause > nul
