import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/auth_service.dart';
import 'services/storage_service.dart';
import 'services/connectivity_service.dart';
import 'services/offline_service.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'screens/camera_screen.dart';
import 'utils/constants.dart';

void main() {
  debugPrint('=== MAIN FUNCTION CALLED ===');
  debugPrint('Time: ${DateTime.now()}');
  debugPrint('============================');

  runZonedGuarded(() {
    debugPrint('main: Ensuring widgets binding initialized...');
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize without clearing cache to prevent double app creation

    debugPrint('main: Running SafeApp...');
    runApp(const SafeApp());
    debugPrint('main: SafeApp started successfully');
  }, (error, stackTrace) {
    debugPrint('=== GLOBAL ERROR IN MAIN ===');
    debugPrint('Error: $error');
    debugPrint('Stack trace: $stackTrace');
    debugPrint('========================');
  });
}

// Safe app wrapper with better error handling
class SafeApp extends StatelessWidget {
  const SafeApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    debugPrint('SafeApp: build() called');

    return MaterialApp(
      title: 'Smart Fire Inspector',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      debugShowCheckedModeBanner: false,
      home: const SafeHome(),
    );
  }
}

class SafeHome extends StatefulWidget {
  const SafeHome({Key? key}) : super(key: key);

  @override
  State<SafeHome> createState() {
    debugPrint('SafeHome: createState() called');
    return _SafeHomeState();
  }
}

class _SafeHomeState extends State<SafeHome> {
  bool _isInitialized = false;
  String _initStatus = 'Initializing...';

  @override
  void initState() {
    super.initState();
    debugPrint('_SafeHomeState: initState() called');
    _initializeApp();
  }

  @override
  void dispose() {
    debugPrint('_SafeHomeState: dispose() called');
    super.dispose();
  }

  Future<void> _initializeApp() async {
    try {
      debugPrint('=== SafeHome: Starting app initialization ===');
      debugPrint('SafeHome: Current time: ${DateTime.now()}');
      debugPrint('SafeHome: Widget mounted: $mounted');

      setState(() {
        _initStatus = 'Initializing storage...';
      });

      debugPrint('SafeHome: About to initialize StorageService...');
      await StorageService.init();
      debugPrint('SafeHome: Storage initialized successfully');

      // Check storage state
      final isAuth = await StorageService.isAuthenticated();
      final token = await StorageService.getToken();
      debugPrint('SafeHome: Storage state - isAuth: $isAuth, hasToken: ${token != null}');

      setState(() {
        _initStatus = 'Initializing offline service...';
      });

      debugPrint('SafeHome: About to initialize OfflineService...');
      await OfflineService.init();
      debugPrint('SafeHome: Offline service initialized successfully');

      setState(() {
        _initStatus = 'Loading app...';
      });

      debugPrint('SafeHome: Waiting 500ms before completing initialization...');
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
        debugPrint('=== SafeHome: App initialization complete ===');
      } else {
        debugPrint('SafeHome: Widget not mounted, skipping setState');
      }
    } catch (e, stackTrace) {
      debugPrint('=== SafeHome: Initialization error ===');
      debugPrint('Error: $e');
      debugPrint('Stack trace: $stackTrace');
      debugPrint('================================');
      if (mounted) {
        setState(() {
          _isInitialized = true; // Show app anyway
          _initStatus = 'Error during initialization, continuing...';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text(
                'Smart Fire Inspector',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                _initStatus,
                style: const TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    // Show the actual app - with try-catch for ultimate safety
    try {
      debugPrint('SafeHome: Creating MultiProvider setup...');
      return MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) {
            debugPrint('SafeHome: Creating ConnectivityService...');
            final service = ConnectivityService();
            debugPrint('SafeHome: ConnectivityService created with hashCode: ${service.hashCode}');
            return service;
          }),
          ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
            create: (context) {
              debugPrint('SafeHome: Creating AuthService...');
              final connectivityService = Provider.of<ConnectivityService>(context, listen: false);
              debugPrint('SafeHome: Got ConnectivityService with hashCode: ${connectivityService.hashCode}');
              final authService = AuthService(connectivityService);
              debugPrint('SafeHome: AuthService created with hashCode: ${authService.hashCode}');
              // Initialize AuthService asynchronously
              debugPrint('SafeHome: Starting AuthService initialization...');
              authService.initialize();
              return authService;
            },
            update: (context, connectivityService, authService) {
              debugPrint('SafeHome: AuthService update called - existing: ${authService?.hashCode}, connectivity: ${connectivityService.hashCode}');
              return authService ?? AuthService(connectivityService);
            },
          ),
        ],
        child: Consumer<AuthService>(
          builder: (context, authService, child) {
            debugPrint('=== SafeHome: Consumer builder called ===');
            debugPrint('AuthService state:');
            debugPrint('  - isLoading: ${authService.isLoading}');
            debugPrint('  - isAuthenticated: ${authService.isAuthenticated}');
            debugPrint('  - user: ${authService.user?.toJson()}');
            debugPrint('  - hashCode: ${authService.hashCode}');
            debugPrint('=====================================');

            if (authService.isLoading) {
              debugPrint('SafeHome: Showing loading screen');
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            try {
              if (authService.isAuthenticated) {
                debugPrint('SafeHome: User authenticated, showing DashboardScreen');
                return const DashboardScreen();
              } else {
                debugPrint('SafeHome: User not authenticated, showing LoginScreen');
                return const LoginScreen();
              }
            } catch (e, stackTrace) {
              debugPrint('=== SafeHome: Consumer error ===');
              debugPrint('Error: $e');
              debugPrint('Stack trace: $stackTrace');
              debugPrint('============================');
              // Ultimate fallback - simple login screen
              return const Scaffold(
                backgroundColor: Colors.white,
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Smart Fire Inspector',
                        style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 16),
                      Text('FALLBACK MODE - Consumer Error'),
                      SizedBox(height: 16),
                      CircularProgressIndicator(),
                    ],
                  ),
                ),
              );
            }
          },
        ),
      );
    } catch (e) {
      debugPrint('SafeHome: Critical error in build: $e');
      // Ultimate fallback
      return const Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Smart Fire Inspector',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('CRITICAL ERROR - FALLBACK MODE'),
              SizedBox(height: 16),
              CircularProgressIndicator(),
            ],
          ),
        ),
      );
    }
  }
}