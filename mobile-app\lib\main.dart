import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'services/auth_service.dart';
import 'services/storage_service.dart';
import 'services/connectivity_service.dart';
import 'services/offline_service.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await StorageService.init();
  await OfflineService.init();

  runApp(const SmartFireInspectorApp());
}

class SmartFireInspectorApp extends StatelessWidget {
  const SmartFireInspectorApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ConnectivityService()),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) => AuthService(
            Provider.of<ConnectivityService>(context, listen: false),
          ),
          update: (context, connectivityService, authService) =>
              authService ?? AuthService(connectivityService),
        ),
      ],
      child: Consumer2<AuthService, ConnectivityService>(
        builder: (context, authService, connectivityService, child) {
          return MaterialApp.router(
            title: Constants.appName,
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: MaterialColor(
                Constants.primaryColor,
                const <int, Color>{
                  50: Color(0xFFEFF6FF),
                  100: Color(0xFFDBEAFE),
                  200: Color(0xFFBFDBFE),
                  300: Color(0xFF93C5FD),
                  400: Color(0xFF60A5FA),
                  500: Color(Constants.primaryColor),
                  600: Color(0xFF2563EB),
                  700: Color(Constants.primaryDarkColor),
                  800: Color(0xFF1E40AF),
                  900: Color(0xFF1E3A8A),
                },
              ),
              useMaterial3: true,
              appBarTheme: const AppBarTheme(
                elevation: 0,
                centerTitle: true,
              ),
              cardTheme: CardTheme(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              inputDecorationTheme: InputDecorationTheme(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            routerConfig: _createRouter(authService),
          );
        },
      ),
    );
  }

  GoRouter _createRouter(AuthService authService) {
    return GoRouter(
      initialLocation: authService.isAuthenticated ? '/dashboard' : '/login',
      redirect: (context, state) {
        final isAuthenticated = authService.isAuthenticated;
        final isLoginRoute = state.location == '/login';

        // If not authenticated and not on login page, redirect to login
        if (!isAuthenticated && !isLoginRoute) {
          return '/login';
        }

        // If authenticated and on login page, redirect to dashboard
        if (isAuthenticated && isLoginRoute) {
          return '/dashboard';
        }

        // No redirect needed
        return null;
      },
      routes: [
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/dashboard',
          name: 'dashboard',
          builder: (context, state) => const DashboardScreen(),
        ),
      ],
    );
  }
}

// Alternative simple navigation approach if GoRouter causes issues
class SimpleApp extends StatelessWidget {
  const SimpleApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ConnectivityService()),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) => AuthService(
            Provider.of<ConnectivityService>(context, listen: false),
          ),
          update: (context, connectivityService, authService) =>
              authService ?? AuthService(connectivityService),
        ),
      ],
      child: MaterialApp(
        title: Constants.appName,
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: MaterialColor(
            Constants.primaryColor,
            const <int, Color>{
              50: Color(0xFFEFF6FF),
              100: Color(0xFFDBEAFE),
              200: Color(0xFFBFDBFE),
              300: Color(0xFF93C5FD),
              400: Color(0xFF60A5FA),
              500: Color(Constants.primaryColor),
              600: Color(0xFF2563EB),
              700: Color(Constants.primaryDarkColor),
              800: Color(0xFF1E40AF),
              900: Color(0xFF1E3A8A),
            },
          ),
          useMaterial3: true,
        ),
        home: Consumer<AuthService>(
          builder: (context, authService, child) {
            if (authService.isLoading) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }
            
            return authService.isAuthenticated 
                ? const DashboardScreen() 
                : const LoginScreen();
          },
        ),
        routes: {
          '/login': (context) => const LoginScreen(),
          '/dashboard': (context) => const DashboardScreen(),
        },
      ),
    );
  }
}
