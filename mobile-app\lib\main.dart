import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/auth_service.dart';
import 'services/storage_service.dart';
import 'services/connectivity_service.dart';
import 'services/offline_service.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'screens/camera_screen.dart';
import 'utils/constants.dart';

void main() async {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    try {
      await inicializujAplikaciu();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Chyba pri inicializácii: $e');
      }
      // Pokračujeme aj pri chybe, aby sa aplikácia spustila
    }

    runApp(const SafeApp());
  }, (error, stackTrace) {
    if (kDebugMode) {
      debugPrint('Critical error in main: $error');
    }
  });
}

Future<void> inicializujAplikaciu() async {
  try {
    // Inicializácia StorageService
    await StorageService.init();

    // Inicializácia OfflineService
    await OfflineService.init();

    // Krátka pauza pre stabilizáciu
    await Future.delayed(const Duration(milliseconds: 100));

  } catch (e, stackTrace) {
    if (kDebugMode) {
      debugPrint('Chyba v inicializujAplikaciu: $e');
      debugPrint('Stack trace: $stackTrace');
    }
    rethrow;
  }
}

class SafeApp extends StatelessWidget {
  const SafeApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Smart Fire Inspector',
      theme: ThemeData(
        primarySwatch: Colors.red,
        primaryColor: const Color(0xFFD32F2F), // Fire red
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFD32F2F),
          brightness: Brightness.light,
        ),
        visualDensity: VisualDensity.adaptivePlatformDensity,
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFFD32F2F),
          foregroundColor: Colors.white,
          elevation: 2,
        ),
      ),
      debugShowCheckedModeBanner: false,
      home: const SafeHome(),
    );
  }
}

class SafeHome extends StatefulWidget {
  const SafeHome({Key? key}) : super(key: key);

  @override
  State<SafeHome> createState() => _SafeHomeState();
}

class _SafeHomeState extends State<SafeHome> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ConnectivityService()),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) {
            final connectivityService = Provider.of<ConnectivityService>(context, listen: false);
            final authService = AuthService(connectivityService);
            authService.initialize();
            return authService;
          },
          update: (context, connectivityService, authService) {
            return authService ?? AuthService(connectivityService);
          },
        ),
      ],
      child: Consumer<AuthService>(
        builder: (context, authService, child) {
          if (authService.isLoading) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (authService.isAuthenticated) {
            return const DashboardScreen();
          } else {
            return const LoginScreen();
          }
        },
      ),
    );
  }
}