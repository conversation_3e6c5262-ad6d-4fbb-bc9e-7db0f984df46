import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'services/auth_service.dart';
import 'services/storage_service.dart';
import 'services/connectivity_service.dart';
import 'services/offline_service.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'screens/camera_screen.dart';
import 'utils/constants.dart';

void main() async {
  // Catch all errors globally
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    try {
      // Initialize services
      await StorageService.init();
      await OfflineService.init();
      debugPrint('Services initialized successfully');
    } catch (e) {
      debugPrint('Error initializing services: $e');
      // Continue anyway - services will reinitialize as needed
    }

    runApp(const SafeApp());
  }, (error, stackTrace) {
    // Global error handler - this will catch errors even in production
    debugPrint('=== GLOBAL ERROR CAUGHT ===');
    debugPrint('Error: $error');
    debugPrint('Stack trace: $stackTrace');
    debugPrint('========================');
  });
}

class SmartFireInspectorApp extends StatelessWidget {
  const SmartFireInspectorApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ConnectivityService()),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) => AuthService(
            Provider.of<ConnectivityService>(context, listen: false),
          ),
          update: (context, connectivityService, authService) =>
              authService ?? AuthService(connectivityService),
        ),
      ],
      child: Consumer2<AuthService, ConnectivityService>(
        builder: (context, authService, connectivityService, child) {
          return MaterialApp.router(
            title: Constants.appName,
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: MaterialColor(
                Constants.primaryColor,
                const <int, Color>{
                  50: Color(0xFFEFF6FF),
                  100: Color(0xFFDBEAFE),
                  200: Color(0xFFBFDBFE),
                  300: Color(0xFF93C5FD),
                  400: Color(0xFF60A5FA),
                  500: Color(Constants.primaryColor),
                  600: Color(0xFF2563EB),
                  700: Color(Constants.primaryDarkColor),
                  800: Color(0xFF1E40AF),
                  900: Color(0xFF1E3A8A),
                },
              ),
              useMaterial3: true,
              appBarTheme: const AppBarTheme(
                elevation: 0,
                centerTitle: true,
              ),
              cardTheme: const CardTheme(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                ),
              ),
              inputDecorationTheme: InputDecorationTheme(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            routerConfig: _createRouter(authService),
          );
        },
      ),
    );
  }

  GoRouter _createRouter(AuthService authService) {
    return GoRouter(
      initialLocation: authService.isAuthenticated ? '/dashboard' : '/login',
      redirect: (context, state) {
        final isAuthenticated = authService.isAuthenticated;
        final isLoginRoute = state.uri.path == '/login';

        // If not authenticated and not on login page, redirect to login
        if (!isAuthenticated && !isLoginRoute) {
          return '/login';
        }

        // If authenticated and on login page, redirect to dashboard
        if (isAuthenticated && isLoginRoute) {
          return '/dashboard';
        }

        // No redirect needed
        return null;
      },
      routes: [
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/dashboard',
          name: 'dashboard',
          builder: (context, state) => const DashboardScreen(),
        ),
      ],
    );
  }
}

// Alternative simple navigation approach if GoRouter causes issues
class SimpleApp extends StatelessWidget {
  const SimpleApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ConnectivityService()),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) => AuthService(
            Provider.of<ConnectivityService>(context, listen: false),
          ),
          update: (context, connectivityService, authService) =>
              authService ?? AuthService(connectivityService),
        ),
      ],
      child: MaterialApp(
        title: Constants.appName,
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: MaterialColor(
            Constants.primaryColor,
            const <int, Color>{
              50: Color(0xFFEFF6FF),
              100: Color(0xFFDBEAFE),
              200: Color(0xFFBFDBFE),
              300: Color(0xFF93C5FD),
              400: Color(0xFF60A5FA),
              500: Color(Constants.primaryColor),
              600: Color(0xFF2563EB),
              700: Color(Constants.primaryDarkColor),
              800: Color(0xFF1E40AF),
              900: Color(0xFF1E3A8A),
            },
          ),
          useMaterial3: true,
        ),
        builder: (context, child) {
          // Global error handling wrapper
          return child ?? const Scaffold(
            body: Center(
              child: Text('Application Error'),
            ),
          );
        },
        home: Consumer<AuthService>(
          builder: (context, authService, child) {
            if (authService.isLoading) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            try {
              return authService.isAuthenticated
                  ? const DashboardScreen()
                  : const LoginScreen();
            } catch (e) {
              // Fallback to login screen on any error
              return const LoginScreen();
            }
          },
        ),
        routes: {
          '/login': (context) => const LoginScreen(),
          '/dashboard': (context) => const DashboardScreen(),
        },
      ),
    );
  }
}

// Safe app wrapper with better error handling
class SafeApp extends StatelessWidget {
  const SafeApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: Constants.appName,
      debugShowCheckedModeBanner: false,
      builder: (context, child) {
        // Error boundary - catch widget build errors
        ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
          debugPrint('=== WIDGET ERROR CAUGHT ===');
          debugPrint('Error: ${errorDetails.exception}');
          debugPrint('Stack trace: ${errorDetails.stack}');
          debugPrint('========================');

          return Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 64),
                  const SizedBox(height: 16),
                  const Text(
                    'Aplikácia sa reštartuje...',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Chyba: ${errorDetails.exception}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        };
        return child ?? const SizedBox();
      },
      theme: ThemeData(
        primarySwatch: MaterialColor(
          Constants.primaryColor,
          const <int, Color>{
            50: Color(0xFFEFF6FF),
            100: Color(0xFFDBEAFE),
            200: Color(0xFFBFDBFE),
            300: Color(0xFF93C5FD),
            400: Color(0xFF60A5FA),
            500: Color(Constants.primaryColor),
            600: Color(0xFF2563EB),
            700: Color(Constants.primaryDarkColor),
            800: Color(0xFF1E40AF),
            900: Color(0xFF1E3A8A),
          },
        ),
        useMaterial3: true,
      ),
      home: const SafeHome(),
    );
  }
}

class SafeHome extends StatefulWidget {
  const SafeHome({Key? key}) : super(key: key);

  @override
  State<SafeHome> createState() => _SafeHomeState();
}

class _SafeHomeState extends State<SafeHome> {
  bool _isInitialized = false;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    debugPrint('SafeHome: Starting initialization...');
    try {
      // Ensure services are initialized
      debugPrint('SafeHome: Initializing StorageService...');
      await StorageService.init();
      debugPrint('SafeHome: StorageService initialized');

      debugPrint('SafeHome: Initializing OfflineService...');
      await OfflineService.init();
      debugPrint('SafeHome: OfflineService initialized');

      // Add small delay to ensure everything is ready
      await Future.delayed(const Duration(milliseconds: 100));

      debugPrint('SafeHome: Setting initialized state...');
      if (mounted) {
        setState(() {
          _isInitialized = true;
          _hasError = false;
        });
      }
      debugPrint('SafeHome: Initialization complete!');
    } catch (e) {
      debugPrint('SafeHome: Error during initialization: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SafeHome: Building widget - isInitialized: $_isInitialized, hasError: $_hasError');

    if (_hasError) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Application Error'),
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                Text(_errorMessage!, style: const TextStyle(fontSize: 12)),
              ],
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _hasError = false;
                    _isInitialized = false;
                  });
                  _initializeApp();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isInitialized) {
      debugPrint('SafeHome: Showing loading screen');
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading...'),
            ],
          ),
        ),
      );
    }

    // Show the actual app
    debugPrint('SafeHome: Creating MultiProvider...');
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) {
          debugPrint('SafeHome: Creating ConnectivityService');
          return ConnectivityService();
        }),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) {
            debugPrint('SafeHome: Creating AuthService');
            final connectivityService = Provider.of<ConnectivityService>(context, listen: false);
            final authService = AuthService(connectivityService);
            // Initialize AuthService asynchronously
            authService.initialize();
            return authService;
          },
          update: (context, connectivityService, authService) {
            debugPrint('SafeHome: Updating AuthService');
            return authService ?? AuthService(connectivityService);
          },
        ),
      ],
      child: Consumer<AuthService>(
        builder: (context, authService, child) {
          debugPrint('SafeHome: Consumer builder - isLoading: ${authService.isLoading}, isAuthenticated: ${authService.isAuthenticated}');

          if (authService.isLoading) {
            debugPrint('SafeHome: Showing auth loading screen');
            return const Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Authenticating...'),
                  ],
                ),
              ),
            );
          }

          try {
            if (authService.isAuthenticated) {
              debugPrint('SafeHome: Showing Dashboard screen');
              return const DashboardScreen();
            } else {
              debugPrint('SafeHome: Showing Login screen');
              return const LoginScreen();
            }
          } catch (e) {
            debugPrint('SafeHome: Error in Consumer builder: $e');
            // Fallback to login screen on any error
            return const LoginScreen();
          }
        },
      ),
    );
  }
}
