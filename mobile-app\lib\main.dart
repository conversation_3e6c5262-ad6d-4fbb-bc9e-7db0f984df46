import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'services/auth_service.dart';
import 'services/storage_service.dart';
import 'services/connectivity_service.dart';
import 'services/offline_service.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'screens/camera_screen.dart';
import 'utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize services
    await StorageService.init();
    await OfflineService.init();
  } catch (e) {
    print('Error initializing services: $e');
    // Continue anyway - services will reinitialize as needed
  }

  runApp(const SafeApp());
}

class SmartFireInspectorApp extends StatelessWidget {
  const SmartFireInspectorApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ConnectivityService()),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) => AuthService(
            Provider.of<ConnectivityService>(context, listen: false),
          ),
          update: (context, connectivityService, authService) =>
              authService ?? AuthService(connectivityService),
        ),
      ],
      child: Consumer2<AuthService, ConnectivityService>(
        builder: (context, authService, connectivityService, child) {
          return MaterialApp.router(
            title: Constants.appName,
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: MaterialColor(
                Constants.primaryColor,
                const <int, Color>{
                  50: Color(0xFFEFF6FF),
                  100: Color(0xFFDBEAFE),
                  200: Color(0xFFBFDBFE),
                  300: Color(0xFF93C5FD),
                  400: Color(0xFF60A5FA),
                  500: Color(Constants.primaryColor),
                  600: Color(0xFF2563EB),
                  700: Color(Constants.primaryDarkColor),
                  800: Color(0xFF1E40AF),
                  900: Color(0xFF1E3A8A),
                },
              ),
              useMaterial3: true,
              appBarTheme: const AppBarTheme(
                elevation: 0,
                centerTitle: true,
              ),
              cardTheme: const CardTheme(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                ),
              ),
              inputDecorationTheme: InputDecorationTheme(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            routerConfig: _createRouter(authService),
          );
        },
      ),
    );
  }

  GoRouter _createRouter(AuthService authService) {
    return GoRouter(
      initialLocation: authService.isAuthenticated ? '/dashboard' : '/login',
      redirect: (context, state) {
        final isAuthenticated = authService.isAuthenticated;
        final isLoginRoute = state.uri.path == '/login';

        // If not authenticated and not on login page, redirect to login
        if (!isAuthenticated && !isLoginRoute) {
          return '/login';
        }

        // If authenticated and on login page, redirect to dashboard
        if (isAuthenticated && isLoginRoute) {
          return '/dashboard';
        }

        // No redirect needed
        return null;
      },
      routes: [
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/dashboard',
          name: 'dashboard',
          builder: (context, state) => const DashboardScreen(),
        ),
      ],
    );
  }
}

// Alternative simple navigation approach if GoRouter causes issues
class SimpleApp extends StatelessWidget {
  const SimpleApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ConnectivityService()),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) => AuthService(
            Provider.of<ConnectivityService>(context, listen: false),
          ),
          update: (context, connectivityService, authService) =>
              authService ?? AuthService(connectivityService),
        ),
      ],
      child: MaterialApp(
        title: Constants.appName,
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: MaterialColor(
            Constants.primaryColor,
            const <int, Color>{
              50: Color(0xFFEFF6FF),
              100: Color(0xFFDBEAFE),
              200: Color(0xFFBFDBFE),
              300: Color(0xFF93C5FD),
              400: Color(0xFF60A5FA),
              500: Color(Constants.primaryColor),
              600: Color(0xFF2563EB),
              700: Color(Constants.primaryDarkColor),
              800: Color(0xFF1E40AF),
              900: Color(0xFF1E3A8A),
            },
          ),
          useMaterial3: true,
        ),
        builder: (context, child) {
          // Global error handling wrapper
          return child ?? const Scaffold(
            body: Center(
              child: Text('Application Error'),
            ),
          );
        },
        home: Consumer<AuthService>(
          builder: (context, authService, child) {
            if (authService.isLoading) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            try {
              return authService.isAuthenticated
                  ? const DashboardScreen()
                  : const LoginScreen();
            } catch (e) {
              // Fallback to login screen on any error
              return const LoginScreen();
            }
          },
        ),
        routes: {
          '/login': (context) => const LoginScreen(),
          '/dashboard': (context) => const DashboardScreen(),
        },
      ),
    );
  }
}

// Safe app wrapper with better error handling
class SafeApp extends StatelessWidget {
  const SafeApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: Constants.appName,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: MaterialColor(
          Constants.primaryColor,
          const <int, Color>{
            50: Color(0xFFEFF6FF),
            100: Color(0xFFDBEAFE),
            200: Color(0xFFBFDBFE),
            300: Color(0xFF93C5FD),
            400: Color(0xFF60A5FA),
            500: Color(Constants.primaryColor),
            600: Color(0xFF2563EB),
            700: Color(Constants.primaryDarkColor),
            800: Color(0xFF1E40AF),
            900: Color(0xFF1E3A8A),
          },
        ),
        useMaterial3: true,
      ),
      home: const SafeHome(),
    );
  }
}

class SafeHome extends StatefulWidget {
  const SafeHome({Key? key}) : super(key: key);

  @override
  State<SafeHome> createState() => _SafeHomeState();
}

class _SafeHomeState extends State<SafeHome> {
  bool _isInitialized = false;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    print('SafeHome: Starting initialization...');
    try {
      // Ensure services are initialized
      print('SafeHome: Initializing StorageService...');
      await StorageService.init();
      print('SafeHome: StorageService initialized');

      print('SafeHome: Initializing OfflineService...');
      await OfflineService.init();
      print('SafeHome: OfflineService initialized');

      print('SafeHome: Setting initialized state...');
      setState(() {
        _isInitialized = true;
        _hasError = false;
      });
      print('SafeHome: Initialization complete!');
    } catch (e) {
      print('SafeHome: Error during initialization: $e');
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    print('SafeHome: Building widget - isInitialized: $_isInitialized, hasError: $_hasError');

    if (_hasError) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Application Error'),
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                Text(_errorMessage!, style: const TextStyle(fontSize: 12)),
              ],
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _hasError = false;
                    _isInitialized = false;
                  });
                  _initializeApp();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isInitialized) {
      print('SafeHome: Showing loading screen');
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading...'),
            ],
          ),
        ),
      );
    }

    // Show the actual app
    print('SafeHome: Creating MultiProvider...');
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) {
          print('SafeHome: Creating ConnectivityService');
          return ConnectivityService();
        }),
        ChangeNotifierProxyProvider<ConnectivityService, AuthService>(
          create: (context) {
            print('SafeHome: Creating AuthService');
            final connectivityService = Provider.of<ConnectivityService>(context, listen: false);
            final authService = AuthService(connectivityService);
            // Initialize AuthService asynchronously
            authService.initialize();
            return authService;
          },
          update: (context, connectivityService, authService) {
            print('SafeHome: Updating AuthService');
            return authService ?? AuthService(connectivityService);
          },
        ),
      ],
      child: Consumer<AuthService>(
        builder: (context, authService, child) {
          print('SafeHome: Consumer builder - isLoading: ${authService.isLoading}, isAuthenticated: ${authService.isAuthenticated}');

          if (authService.isLoading) {
            print('SafeHome: Showing auth loading screen');
            return const Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Authenticating...'),
                  ],
                ),
              ),
            );
          }

          try {
            if (authService.isAuthenticated) {
              print('SafeHome: Showing Dashboard screen');
              return Scaffold(
                appBar: AppBar(
                  title: const Text('Smart Fire Inspector'),
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('Dashboard', style: TextStyle(fontSize: 24)),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const CameraScreen()),
                          );
                        },
                        child: const Text('Fotoaparát'),
                      ),
                    ],
                  ),
                ),
              );
            } else {
              print('SafeHome: Showing Login screen');
              return const Scaffold(
                backgroundColor: Colors.white,
                body: Center(
                  child: Text(
                    'LOGIN SCREEN WORKS!',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
              );
            }
          } catch (e) {
            print('SafeHome: Error in Consumer builder: $e');
            // Fallback to login screen on any error
            return Scaffold(
              body: Center(
                child: Text('Error: $e', style: const TextStyle(color: Colors.red)),
              ),
            );
          }
        },
      ),
    );
  }
}
