@echo off
echo ========================================
echo Smart Fire Inspector - Environment Setup
echo ========================================
echo.

echo Tento skript skontroluje a nakonfiguruje vyvojarske prostredie.
echo.

echo [1/5] Kontrolujem .NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo CHYBA: .NET SDK nie je nainstalovany alebo nie je v PATH
    echo Stiahnite z: https://dotnet.microsoft.com/download
    set DOTNET_OK=0
) else (
    for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
    echo OK: .NET SDK verzia !DOTNET_VERSION!
    set DOTNET_OK=1
)

echo.
echo [2/5] Kontrolujem Node.js...
node --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo CHYBA: Node.js nie je nainstalovany alebo nie je v PATH
    echo Stiahnite z: https://nodejs.org/
    set NODE_OK=0
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo OK: Node.js verzia !NODE_VERSION!
    set NODE_OK=1
)

echo.
echo [3/5] Kontrolujem Flutter SDK...
C:\flutter\bin\flutter.bat --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo CHYBA: Flutter SDK nie je nainstalovany alebo nie je v C:\flutter\
    echo Stiahnite z: https://flutter.dev/docs/get-started/install
    set FLUTTER_OK=0
) else (
    echo OK: Flutter SDK nainstalovany
    set FLUTTER_OK=1
)

echo.
echo [4/5] Kontrolujem Android SDK...
if exist "C:\Android\android-sdk\platform-tools\adb.exe" (
    echo OK: Android SDK platform-tools najdene
    set ANDROID_OK=1
) else (
    echo CHYBA: Android SDK nie je nainstalovany v C:\Android\android-sdk\
    echo Nainstalujte Android Studio a SDK
    set ANDROID_OK=0
)

echo.
echo [5/5] Kontrolujem zdrojove adresare...
set SOURCE_BACKEND=c:\SmartFireInspector\SmartFireInspectorPublish\backend
set SOURCE_WEBAPP=c:\SmartFireInspector\SmartFireInspectorPublish\web-app
set SOURCE_MOBILE=c:\SmartFireInspector\SmartFireInspectorPublish\mobile-app

if exist "%SOURCE_BACKEND%" (
    echo OK: Backend adresar existuje
    set BACKEND_OK=1
) else (
    echo POZOR: Backend adresar neexistuje: %SOURCE_BACKEND%
    echo (Toto je normalne ak nemame backend projekt)
    set BACKEND_OK=0
)

if exist "%SOURCE_WEBAPP%" (
    echo OK: Web app adresar existuje
    set WEBAPP_OK=1
) else (
    echo POZOR: Web app adresar neexistuje: %SOURCE_WEBAPP%
    echo (Toto je normalne ak nemame web app projekt)
    set WEBAPP_OK=0
)

if exist "%SOURCE_MOBILE%" (
    echo OK: Mobile app adresar existuje
    set MOBILE_OK=1
) else (
    echo POZOR: Mobile app adresar neexistuje: %SOURCE_MOBILE%
    echo (Toto je normalne ak nemame mobile app projekt)
    set MOBILE_OK=0
)

echo.
echo ========================================
echo SUHRN KONTROLY PROSTREDIA:
echo ========================================

if "%DOTNET_OK%"=="1" (
    echo [OK] .NET SDK: !DOTNET_VERSION!
) else (
    echo [CHYBA] .NET SDK: Nie je nainstalovany
)

if "%NODE_OK%"=="1" (
    echo [OK] Node.js: !NODE_VERSION!
) else (
    echo [CHYBA] Node.js: Nie je nainstalovany
)

if "%FLUTTER_OK%"=="1" (
    echo [OK] Flutter SDK: Nainstalovany
) else (
    echo [CHYBA] Flutter SDK: Nie je nainstalovany
)

if "%ANDROID_OK%"=="1" (
    echo [OK] Android SDK: Nainstalovany
) else (
    echo [CHYBA] Android SDK: Nie je nainstalovany
)

if "%BACKEND_OK%"=="1" (
    echo [OK] Backend projekt: Existuje
) else (
    echo [CHYBA] Backend projekt: Neexistuje
)

if "%WEBAPP_OK%"=="1" (
    echo [OK] Web app projekt: Existuje
) else (
    echo [CHYBA] Web app projekt: Neexistuje
)

if "%MOBILE_OK%"=="1" (
    echo [OK] Mobile app projekt: Existuje
) else (
    echo [CHYBA] Mobile app projekt: Neexistuje
)

echo.
if "%DOTNET_OK%%NODE_OK%%FLUTTER_OK%%ANDROID_OK%%BACKEND_OK%%WEBAPP_OK%%MOBILE_OK%"=="1111111" (
    echo ========================================
    echo USPECH: Vsetko je pripravene!
    echo ========================================
    echo Mozete pokracovat s publish skriptami.
) else (
    echo ========================================
    echo POZOR: Niektore komponenty chybaju!
    echo ========================================
    echo Opravte chyby pred pokracovanim.
)

echo.
pause
