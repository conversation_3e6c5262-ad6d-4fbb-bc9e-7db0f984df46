import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

class ConnectivityService extends ChangeNotifier {
  bool _isOnline = true;
  Timer? _connectivityTimer;
  
  bool get isOnline => _isOnline;
  bool get isOffline => !_isOnline;

  ConnectivityService() {
    _startConnectivityCheck();
  }

  void _startConnectivityCheck() {
    // Check connectivity every 10 seconds
    _connectivityTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkConnectivity();
    });
    
    // Initial check
    _checkConnectivity();
  }

  Future<void> _checkConnectivity() async {
    try {
      // For web, assume always online for now
      if (kIsWeb) {
        _isOnline = true;
        return;
      }

      final result = await InternetAddress.lookup('google.com');
      final wasOffline = !_isOnline;

      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        _isOnline = true;
        if (wasOffline) {
          print('📶 Connection restored - back online');
          notifyListeners();
        }
      } else {
        _setOffline();
      }
    } on SocketException catch (_) {
      _setOffline();
    } catch (e) {
      print('Connectivity check error: $e');
      // For web, assume online on error
      if (kIsWeb) {
        _isOnline = true;
      } else {
        _setOffline();
      }
    }
  }

  void _setOffline() {
    if (_isOnline) {
      print('📵 Connection lost - going offline');
      _isOnline = false;
      notifyListeners();
    }
  }

  Future<bool> checkConnectivity() async {
    await _checkConnectivity();
    return _isOnline;
  }

  @override
  void dispose() {
    _connectivityTimer?.cancel();
    super.dispose();
  }
}
