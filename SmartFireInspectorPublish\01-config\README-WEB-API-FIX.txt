========================================
Smart Fire Inspector - Web API Config Fix
========================================

PROBLEM:
========

web-api-config.bat po zadani moznosti 2 a viac nie je mozne zadat hodnotu,
okno sa hned zavrie.

PRICINA:
========

1. DELAYED EXPANSION:
   - Chybalo setlocal enabledelayedexpansion (uz opravene)

2. IF-ELSE STRUKTURA:
   - Komplexne vnorene if-else bloky sposobovali problemy
   - Batch interpreter mal problemy s dlhymi if-else retazcami

RIESENIE:
=========

PRED OPRAVOU (nefungovalo):
if "%choice%"=="1" (
    set API_URL=...
) else if "%choice%"=="2" (
    set /p custom_port="Port: "
    set API_URL=http://localhost:!custom_port!/api
) else if "%choice%"=="3" (
    ...
)

PO OPRAVE (funguje):
if "%choice%"=="1" goto :option1
if "%choice%"=="2" goto :option2
if "%choice%"=="3" goto :option3
if "%choice%"=="4" goto :option4
if "%choice%"=="5" goto :option5

:option1
set API_URL=http://localhost:5000/api
goto :create_config

:option2
set /p custom_port="Port: "
set API_URL=http://localhost:!custom_port!/api
goto :create_config

:create_config
echo // API Configuration > "%CONFIG_FILE%"
...

VYHODY NOVEHO PRISTUPU:
======================

1. JEDNODUCHOST:
   - Kazda moznost ma vlastny label
   - Ziadne vnorene if-else bloky
   - Jasna struktura kodu

2. STABILITA:
   - Batch interpreter lepsie spracovava goto prikazy
   - Menej problemov s premennyni
   - Robustnejsie error handling

3. UDRZBA:
   - Lahsie pridavanie novych moznosti
   - Jasne oddelenie logiky
   - Lepsia citatelnost kodu

TESTOVANIE:
===========

1. MANUAL TEST:
   debug-web-api-config.bat
   - Testuje vsetky moznosti s debug informaciami
   - Zobrazuje hodnoty premennych
   - Overuje vytvorenie suboru

2. AUTOMATED TEST:
   test-web-api-config.bat
   - Automaticky testuje vsetky moznosti
   - Vytvara backup konfiguracii
   - Overuje funkcionalnost delayed expansion

3. INTEGRATION TEST:
   - Spustite web-api-config.bat
   - Vyberte moznost 2-5
   - Zadajte testovaci text
   - Skontrolujte vytvoreny constants.js

MOZNOSTI KONFIGURACII:
======================

1. LOCALHOST (automaticky):
   http://localhost:5000/api

2. CUSTOM PORT (zadajte port):
   http://localhost:XXXX/api
   Priklad: 8080 -> http://localhost:8080/api

3. CUSTOM IP (zadajte IP + port):
   http://192.168.1.X:YYYY/api
   Priklad: *************, 5000 -> http://*************:5000/api

4. PRODUKCIA (zadajte URL):
   https://your-domain.com/api
   Priklad: https://api.smartfire.sk

5. CUSTOM URL (zadajte kompletnu URL):
   http://custom-server:port/path
   Priklad: http://*************:3000/api

VYTVORENY SUBOR:
================

CESTA: c:\SmartFireInspector\web-app\src\utils\constants.js

OBSAH:
// API Configuration
// Lokalny development - localhost:8080
export const API_BASE_URL = 'http://localhost:8080/api';

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    TEST_ACCOUNTS: '/auth/test-accounts'
  },
  DEVICES: {
    LIST: '/devices',
    CREATE: '/devices',
    UPDATE: '/devices',
    DELETE: '/devices'
  },
  INSPECTIONS: {
    LIST: '/inspections',
    CREATE: '/inspections',
    UPDATE: '/inspections',
    DELETE: '/inspections'
  },
  REPORTS: {
    LIST: '/reports',
    CREATE: '/reports',
    EXPORT: '/reports/export'
  }
};

// Storage Keys
export const STORAGE_KEYS = {
  TOKEN: 'smartfire_token',
  USER: 'smartfire_user'
};

// User Roles
export const USER_ROLES = {
  ADMIN: 'Admin',
  INSPECTOR: 'Inspector',
  USER: 'User'
};

// Request Configuration
export const REQUEST_CONFIG = {
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
};

POUZITIE V REACT KODE:
=====================

import { API_BASE_URL, API_ENDPOINTS } from '../utils/constants';
import axios from 'axios';

// Priklad API volania
const response = await axios.get(`${API_BASE_URL}${API_ENDPOINTS.AUTH.LOGIN}`);

// Alebo v api.js
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000
});

TROUBLESHOOTING:
================

PROBLEM: "Okno sa zavrie po zadani moznosti"
RIESENIE: 
1. Spustite ako administrator
2. Pouzite Command Prompt namiesto PowerShell
3. Skontrolujte, ci je setlocal enabledelayedexpansion na zaciatku

PROBLEM: "Premenne sa nezapisuju"
RIESENIE:
1. Pouzite !variable! namiesto %variable%
2. Skontrolujte delayed expansion
3. Testujte s debug-web-api-config.bat

PROBLEM: "Subor sa nevytvori"
RIESENIE:
1. Skontrolujte, ci existuje src\utils adresar
2. Skontrolujte prava na zapis
3. Spustite ako administrator

PROBLEM: "Neplatna volba"
RIESENIE:
1. Zadajte cislo 1-5
2. Nestlacajte Enter bez zadania cisla
3. Pouzite len cisla, nie pismena

POSLEDNA AKTUALIZACIA: 2025-01-05
