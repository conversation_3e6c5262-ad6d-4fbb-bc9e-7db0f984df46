@echo off
echo ========================================
echo Smart Fire Inspector - Mobile App Build
echo ========================================
echo.

set SOURCE_DIR=c:\SmartFireInspector\mobile-app
set PUBLISH_DIR=c:\SmartFireInspectorPublish\published\mobile-app

echo Kontrolujem zdrojovy adresar...
if not exist "%SOURCE_DIR%" (
    echo CHYBA: Zdrojovy adresar neexistuje: %SOURCE_DIR%
    pause
    exit /b 1
)

echo Vytvariam publish adresar...
if not exist "%PUBLISH_DIR%" mkdir "%PUBLISH_DIR%" 2>nul

echo.
echo Vyberte typ buildu:
echo 1. Debug APK (rychly build pre testovanie)
echo 2. Release APK (optimalizovany pre nasadenie)
echo 3. App Bundle (pre Google Play Store)
echo 4. Vsetky typy (Debug + Release + Bundle)
echo.
set /p build_choice="Zadajte volbu (1-4): "

cd /d "%SOURCE_DIR%"

echo.
echo Kontrolujem Flutter dependencies...
call C:\flutter\bin\flutter.bat clean
call C:\flutter\bin\flutter.bat pub get

if %ERRORLEVEL% neq 0 (
    echo.
    echo CHYBA: Flutter pub get zlyhal!
    pause
    exit /b 1
)

echo.
if "%build_choice%"=="1" (
    echo Spustam debug build...
    call C:\flutter\bin\flutter.bat build apk --debug
    set BUILD_FILE=build\app\outputs\flutter-apk\app-debug.apk
    set OUTPUT_NAME=smart-fire-inspector-debug.apk
    goto :copy_files
) else if "%build_choice%"=="2" (
    echo Spustam release build...
    call C:\flutter\bin\flutter.bat build apk --release
    set BUILD_FILE=build\app\outputs\flutter-apk\app-release.apk
    set OUTPUT_NAME=smart-fire-inspector-release.apk
    goto :copy_files
) else if "%build_choice%"=="3" (
    echo Spustam app bundle build...
    call C:\flutter\bin\flutter.bat build appbundle --release
    set BUILD_FILE=build\app\outputs\bundle\release\app-release.aab
    set OUTPUT_NAME=smart-fire-inspector-release.aab
    goto :copy_files
) else if "%build_choice%"=="4" (
    echo Spustam vsetky typy buildov...
    
    echo [1/3] Debug APK...
    call C:\flutter\bin\flutter.bat build apk --debug
    if %ERRORLEVEL% neq 0 goto :build_error
    copy "build\app\outputs\flutter-apk\app-debug.apk" "%PUBLISH_DIR%\smart-fire-inspector-debug.apk"
    
    echo [2/3] Release APK...
    call C:\flutter\bin\flutter.bat build apk --release
    if %ERRORLEVEL% neq 0 goto :build_error
    copy "build\app\outputs\flutter-apk\app-release.apk" "%PUBLISH_DIR%\smart-fire-inspector-release.apk"
    
    echo [3/3] App Bundle...
    call C:\flutter\bin\flutter.bat build appbundle --release
    if %ERRORLEVEL% neq 0 goto :build_error
    copy "build\app\outputs\bundle\release\app-release.aab" "%PUBLISH_DIR%\smart-fire-inspector-release.aab"
    
    goto :create_readme
) else (
    echo Neplatna volba!
    pause
    exit /b 1
)

:copy_files
if %ERRORLEVEL% neq 0 goto :build_error

echo.
echo Kopirujeme build subor...
copy "%BUILD_FILE%" "%PUBLISH_DIR%\%OUTPUT_NAME%"

:create_readme
echo.
echo Vytvariam README.txt...
echo Smart Fire Inspector - Mobile Application > "%PUBLISH_DIR%\README.txt"
echo ========================================== >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Build datum: %date% %time% >> "%PUBLISH_DIR%\README.txt"
echo Build typ: %build_choice% >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Dostupne subory: >> "%PUBLISH_DIR%\README.txt"
dir /b "%PUBLISH_DIR%\*.apk" "%PUBLISH_DIR%\*.aab" 2>nul >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Pre instalaciu: >> "%PUBLISH_DIR%\README.txt"
echo 1. Pripojte Android zariadenie cez USB >> "%PUBLISH_DIR%\README.txt"
echo 2. Povolte Developer Options a USB Debugging >> "%PUBLISH_DIR%\README.txt"
echo 3. Pouzite deploy skripty alebo manualne: >> "%PUBLISH_DIR%\README.txt"
echo    adb install nazov-suboru.apk >> "%PUBLISH_DIR%\README.txt"
echo. >> "%PUBLISH_DIR%\README.txt"
echo Poznamky: >> "%PUBLISH_DIR%\README.txt"
echo - Debug APK: Pre testovanie, vacsi subor >> "%PUBLISH_DIR%\README.txt"
echo - Release APK: Optimalizovany, mensi subor >> "%PUBLISH_DIR%\README.txt"
echo - App Bundle: Pre Google Play Store >> "%PUBLISH_DIR%\README.txt"

echo.
echo Kontrolujem velkost suborov...
dir "%PUBLISH_DIR%\*.apk" "%PUBLISH_DIR%\*.aab" 2>nul

echo.
echo ========================================
echo USPECH: Mobile App build dokonceny!
echo ========================================
echo Umiestnenie: %PUBLISH_DIR%
echo.
pause
exit /b 0

:build_error
echo.
echo CHYBA: Flutter build zlyhal!
pause
exit /b 1
