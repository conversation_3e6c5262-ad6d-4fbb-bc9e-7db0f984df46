name: smart_fire_inspector
description: Smart Fire Inspector mobile application for fire safety device inspections.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # HTTP client
  http: ^1.1.0

  # Secure storage (temporarily disabled for web)
  # flutter_secure_storage: ^9.0.0

  # Shared preferences
  shared_preferences: ^2.2.2

  # Camera functionality (temporarily disabled for troubleshooting)
  # camera: ^0.10.5+9
  # path_provider: ^2.1.2
  # path: ^1.8.3

  # JSON serialization
  json_annotation: ^4.8.1

  # State management
  provider: ^6.1.1

  # Navigation
  go_router: ^12.1.3

  # Connectivity (temporarily disabled for web)
  # connectivity_plus: ^5.0.2

  # UI components
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  json_serializable: ^6.7.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: fonts/Roboto-Regular.ttf
  #       - asset: fonts/Roboto-Bold.ttf
  #         weight: 700
