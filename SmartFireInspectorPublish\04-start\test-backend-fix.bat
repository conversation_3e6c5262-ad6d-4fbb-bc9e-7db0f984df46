@echo off
echo ========================================
echo Test Backend Fix - Smart Fire Inspector
echo ========================================
echo.

echo Testujem opravu start-backend-dev.bat...
echo.

set BACKEND_DIR=c:\SmartFireInspector\backend

echo [1] Kontrolujem backend adresar...
if exist "%BACKEND_DIR%" (
    echo [OK] Backend adresar existuje: %BACKEND_DIR%
) else (
    echo [CHYBA] Backend adresar neexistuje: %BACKEND_DIR%
    pause
    exit /b 1
)

echo.
echo [2] Kontrolujem .NET SDK...
dotnet --version > dotnet_version.tmp 2>NUL
if exist dotnet_version.tmp (
    set /p dotnet_ver=<dotnet_version.tmp
    echo [OK] .NET SDK verzia: %dotnet_ver%
    del dotnet_version.tmp
) else (
    echo [CHYBA] .NET SDK nie je nainstalovany!
    pause
    exit /b 1
)

echo.
echo [3] Kontrolujem beziacich procesov...
tasklist /FI "IMAGENAME eq SmartFireInspector.Api.exe" 2>NUL | find /I /N "SmartFireInspector.Api.exe" > NUL
if "%ERRORLEVEL%"=="0" (
    echo [POZOR] SmartFireInspector.Api.exe uz bezi!
    echo Toto moze sposobit file lock error.
    echo Odporucam spustit cleanup-backend.bat najprv.
) else (
    echo [OK] SmartFireInspector.Api.exe nie je spusteny.
)

echo.
echo [4] Kontrolujem port 5000...
netstat -ano | findstr :5000 > port_check.tmp 2>NUL
if exist port_check.tmp (
    echo [POZOR] Port 5000 je obsadeny:
    type port_check.tmp
    del port_check.tmp
    echo Toto moze sposobit problemy pri spusteni.
) else (
    echo [OK] Port 5000 je volny.
)

echo.
echo [5] Kontrolujem projekt subory...
cd /d "%BACKEND_DIR%"

if exist "SmartFireInspector.Api.csproj" (
    echo [OK] SmartFireInspector.Api.csproj existuje
) else (
    echo [CHYBA] SmartFireInspector.Api.csproj neexistuje!
    pause
    exit /b 1
)

if exist "Program.cs" (
    echo [OK] Program.cs existuje
) else (
    echo [CHYBA] Program.cs neexistuje!
    pause
    exit /b 1
)

echo.
echo [6] Test dotnet restore...
echo Spustam dotnet restore...
dotnet restore > restore_output.tmp 2>&1
if %ERRORLEVEL% equ 0 (
    echo [OK] dotnet restore uspesny
) else (
    echo [CHYBA] dotnet restore zlyhal:
    type restore_output.tmp
)
if exist restore_output.tmp del restore_output.tmp

echo.
echo [7] Test dotnet build...
echo Spustam dotnet build...
dotnet build > build_output.tmp 2>&1
if %ERRORLEVEL% equ 0 (
    echo [OK] dotnet build uspesny
) else (
    echo [CHYBA] dotnet build zlyhal:
    type build_output.tmp
    echo.
    echo Toto je pravdepodobne pricina problemov!
)
if exist build_output.tmp del build_output.tmp

echo.
echo ========================================
echo TEST DOKONCENY!
echo ========================================
echo.

echo SUHRN:
echo - Backend adresar: OK
echo - .NET SDK: OK
echo - Projekt subory: OK
echo.

echo ODPORUCANIA:
echo 1. Ak su procesy spustene, spustite: cleanup-backend.bat
echo 2. Ak build zlyhal, opravte chyby v kode
echo 3. Potom spustite: start-backend-dev.bat
echo.

echo Stlacte lubovolnu klavesu...
pause > nul
