@echo off
setlocal enabledelayedexpansion
title Smart Fire Inspector - Master Control Panel
color 0A

REM Nastavenie spravnej cesty
cd /d "%~dp0"

:main_menu
cls
echo.
echo ========================================
echo   Smart Fire Inspector - Master Menu
echo ========================================
echo.
echo Aktualna cesta: %CD%
echo.
echo Dostupne skripty:
if exist "01-config\environment-setup.bat" (echo [OK] Kontrola prostredia) else (echo [CHYBA] Kontrola prostredia)
if exist "01-config\mobile-api-config.bat" (echo [OK] Mobile API config) else (echo [CHYBA] Mobile API config)
if exist "04-start\start-all-services.bat" (echo [OK] Start services) else (echo [CHYBA] Start services)
echo.
echo Vyberte akciu:
echo.
echo [1] Kontrola prostredia
echo [2] Konfiguracia mobile API
echo [3] Publikovat vsetko
echo [4] Spustit vsetky sluzby
echo [5] Spustit len Backend API
echo [6] Test funkcnosti
echo.
echo [0] Ukoncit
echo.
set /p choice="Zadajte volbu (0-6): "

echo.
echo DEBUG: Vybrana volba: '%choice%'
echo.

if "%choice%"=="0" goto :exit
if "%choice%"=="1" goto :env_check
if "%choice%"=="2" goto :mobile_config
if "%choice%"=="3" goto :publish_all
if "%choice%"=="4" goto :start_all
if "%choice%"=="5" goto :start_backend
if "%choice%"=="6" goto :test_function

echo Neplatna volba '%choice%'! Stlacte lubovolnu klavesu...
pause > nul
goto :main_menu

:env_check
echo Spustam kontrolu prostredia...
echo.
if exist "01-config\environment-setup.bat" (
    echo Subor existuje, spustam...
    call "01-config\environment-setup.bat"
    echo.
    echo Navrat z kontroly prostredia.
) else (
    echo CHYBA: Subor 01-config\environment-setup.bat neexistuje!
    echo Aktualna cesta: %CD%
    dir 01-config\
)
echo.
echo Stlacte lubovolnu klavesu pre navrat do menu...
pause > nul
goto :main_menu

:mobile_config
echo Spustam konfiguraciu mobile API...
echo.
if exist "01-config\mobile-api-config.bat" (
    echo Subor existuje, spustam...
    call "01-config\mobile-api-config.bat"
    echo.
    echo Navrat z mobile API config.
) else (
    echo CHYBA: Subor 01-config\mobile-api-config.bat neexistuje!
    echo Aktualna cesta: %CD%
    dir 01-config\
)
echo.
echo Stlacte lubovolnu klavesu pre navrat do menu...
pause > nul
goto :main_menu

:publish_all
echo Spustam publikovanie vsetkych komponentov...
echo.
if exist "02-publish\publish-all.bat" (
    echo Subor existuje, spustam...
    call "02-publish\publish-all.bat"
    echo.
    echo Navrat z publish all.
) else (
    echo CHYBA: Subor 02-publish\publish-all.bat neexistuje!
    echo Aktualna cesta: %CD%
    dir 02-publish\
)
echo.
echo Stlacte lubovolnu klavesu pre navrat do menu...
pause > nul
goto :main_menu

:start_all
echo Spustam vsetky sluzby...
echo.
if exist "04-start\start-all-services.bat" (
    echo Subor existuje, spustam...
    call "04-start\start-all-services.bat"
    echo.
    echo Navrat z start all services.
) else (
    echo CHYBA: Subor 04-start\start-all-services.bat neexistuje!
    echo Aktualna cesta: %CD%
    dir 04-start\
)
echo.
echo Stlacte lubovolnu klavesu pre navrat do menu...
pause > nul
goto :main_menu

:start_backend
echo Spustam Backend API...
echo.
if exist "04-start\start-backend-dev.bat" (
    echo Subor existuje, spustam...
    call "04-start\start-backend-dev.bat"
    echo.
    echo Navrat z start backend.
) else (
    echo CHYBA: Subor 04-start\start-backend-dev.bat neexistuje!
    echo Aktualna cesta: %CD%
    dir 04-start\
)
echo.
echo Stlacte lubovolnu klavesu pre navrat do menu...
pause > nul
goto :main_menu

:test_function
echo Test funkcnosti...
echo.
echo Aktualna cesta: %CD%
echo Datum a cas: %date% %time%
echo.
echo Kontrolujem adresare:
if exist "01-config" (echo [OK] 01-config) else (echo [CHYBA] 01-config)
if exist "02-publish" (echo [OK] 02-publish) else (echo [CHYBA] 02-publish)
if exist "03-deploy" (echo [OK] 03-deploy) else (echo [CHYBA] 03-deploy)
if exist "04-start" (echo [OK] 04-start) else (echo [CHYBA] 04-start)
echo.
echo Test dokonceny.
echo.
echo Stlacte lubovolnu klavesu pre navrat do menu...
pause > nul
goto :main_menu

:exit
cls
echo.
echo ========================================
echo   Smart Fire Inspector - Ukoncenie
echo ========================================
echo.
echo Dakujeme za pouzitie Smart Fire Inspector!
echo.
echo Stlacte lubovolnu klavesu pre ukoncenie...
pause > nul
exit /b 0
