@echo off
setlocal enabledelayedexpansion
echo ========================================
echo Smart Fire Inspector - Deploy to USB Device
echo ========================================
echo.

set SOURCE_DIR=c:\SmartFireInspector\mobile-app
set PUBLISH_DIR=c:\SmartFireInspector\SmartFireInspectorPublish\published\mobile-app

echo Kontrolujem pripojene zariadenia...
call C:\Android\android-sdk\platform-tools\adb.exe devices > devices_temp.txt

echo.
echo Dostupne zariadenia:
echo.

REM Parsovanie zariadeni z adb devices
set device_count=0
for /f "skip=1 tokens=1,2" %%a in (devices_temp.txt) do (
    if "%%b"=="device" (
        set /a device_count+=1
        set device_!device_count!=%%a
        echo [!device_count!] %%a

        REM Ziskanie nazvu zariadenia
        call C:\Android\android-sdk\platform-tools\adb.exe -s %%a shell getprop ro.product.model > model_temp.txt
        set /p device_model=<model_temp.txt
        echo     Model: !device_model!
        del model_temp.txt
        echo.
    )
)
del devices_temp.txt

if %device_count%==0 (
    echo CHYBA: Ziadne zariadenia nie su pripojene!
    echo.
    echo Skontrolujte:
    echo 1. USB pripojenie
    echo 2. Developer Options su povolene
    echo 3. USB Debugging je povoleny
    echo 4. Zariadenie je odomknute
    echo.
    pause
    exit /b 1
)

if %device_count%==1 (
    echo Najdene 1 zariadenie, pouzivam automaticky: !device_1!
    set SELECTED_DEVICE=!device_1!
) else (
    echo Najdenych %device_count% zariadeni.
    echo.
    set /p device_choice="Vyberte zariadenie (1-%device_count%): "

    if !device_choice! lss 1 (
        echo Neplatna volba!
        pause
        exit /b 1
    )
    if !device_choice! gtr %device_count% (
        echo Neplatna volba!
        pause
        exit /b 1
    )

    call set SELECTED_DEVICE=%%device_!device_choice!%%
)

echo.
echo Vybrane zariadenie: %SELECTED_DEVICE%
echo.

echo POZOR: Pred pokracovanim sa uistite, ze:
echo 1. Zariadenie je odomknute
echo 2. Mate povolenu instalaciu z neznamy zdrojov
echo 3. USB Debugging je povoleny
echo.
set /p continue="Pokracovat s nasadenim? (y/n): "
if /i not "%continue%"=="y" exit /b 0

echo.
echo Vyberte sposob nasadenia:
echo 1. Nasadit z uz vybudovaneho APK
echo 2. Build a nasadit debug verziu (rychle)
echo 3. Build a nasadit release verziu
echo.
set /p deploy_choice="Zadajte volbu (1-3): "

if "%deploy_choice%"=="1" (
    echo.
    echo Dostupne APK subory:
    if exist "%PUBLISH_DIR%\*.apk" (
        dir /b "%PUBLISH_DIR%\*.apk"
        echo.
        set /p apk_name="Zadajte nazov APK suboru: "
        set APK_PATH=%PUBLISH_DIR%\!apk_name!
    ) else (
        echo Ziadne APK subory nenajdene v %PUBLISH_DIR%
        echo Najprv spustite build-mobile.bat
        pause
        exit /b 1
    )
) else if "%deploy_choice%"=="2" (
    echo.
    echo Spustam debug build...
    cd /d "%SOURCE_DIR%"
    call C:\flutter\bin\flutter.bat build apk --debug
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Build zlyhal!
        pause
        exit /b 1
    )
    set APK_PATH=%SOURCE_DIR%\build\app\outputs\flutter-apk\app-debug.apk
) else if "%deploy_choice%"=="3" (
    echo.
    echo Spustam release build...
    cd /d "%SOURCE_DIR%"
    call C:\flutter\bin\flutter.bat build apk --release
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Build zlyhal!
        pause
        exit /b 1
    )
    set APK_PATH=%SOURCE_DIR%\build\app\outputs\flutter-apk\app-release.apk
) else (
    echo Neplatna volba!
    pause
    exit /b 1
)

echo.
echo Kontrolujem APK subor...
if not exist "%APK_PATH%" (
    echo CHYBA: APK subor neexistuje: %APK_PATH%
    pause
    exit /b 1
)

echo.
echo Kontrolujem vybrane zariadenie: %SELECTED_DEVICE%
call C:\Android\android-sdk\platform-tools\adb.exe -s %SELECTED_DEVICE% get-state > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo CHYBA: Vybrane zariadenie %SELECTED_DEVICE% nie je dostupne!
    echo Skontrolujte USB pripojenie.
    pause
    exit /b 1
)

echo.
echo Odinstaluvavam predchadzajucu verziu (ak existuje)...
call C:\Android\android-sdk\platform-tools\adb.exe -s %SELECTED_DEVICE% uninstall com.example.smart_fire_inspector

echo.
echo Instalujem novu verziu na zariadenie: %SELECTED_DEVICE%
echo APK: %APK_PATH%
call C:\Android\android-sdk\platform-tools\adb.exe -s %SELECTED_DEVICE% install "%APK_PATH%"

if %ERRORLEVEL% neq 0 (
    echo.
    echo CHYBA: Instalacia zlyhala!
    echo.
    echo Mozne priciny:
    echo - Zariadenie nie je pripojene
    echo - USB Debugging nie je povoleny
    echo - Instalacia z neznamy zdrojov nie je povolena
    echo - APK subor je poskodeny
    echo - Nedostatok miesta na zariadeni
    echo.
    pause
    exit /b 1
)

echo.
echo Spustam aplikaciu na zariadeni: %SELECTED_DEVICE%
call C:\Android\android-sdk\platform-tools\adb.exe -s %SELECTED_DEVICE% shell am start -n com.example.smart_fire_inspector/com.example.smart_fire_inspector.MainActivity

echo.
echo Kontrolujem ci sa aplikacia spustila...
timeout /t 3 /nobreak > nul
call C:\Android\android-sdk\platform-tools\adb.exe -s %SELECTED_DEVICE% shell "ps | grep smart_fire_inspector" > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo Aplikacia sa uspesne spustila na zariadeni: %SELECTED_DEVICE%
) else (
    echo Aplikacia sa mozno nespustila automaticky. Skuste ju spustit manualne na zariadeni.
)

echo.
echo ========================================
echo USPECH: Aplikacia nasadena na zariadenie!
echo ========================================
echo APK: %APK_PATH%
echo Zariadenie: %SELECTED_DEVICE%
echo.
echo Aplikacia by sa mala automaticky spustit na zariadeni.
echo Ak nie, najdite ju v menu aplikacii.
echo.
echo Pre nasadenie na ine zariadenie spustite skript znovu.
echo.
pause
