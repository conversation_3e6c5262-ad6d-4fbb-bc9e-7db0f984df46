@echo off
title Smart Fire Inspector - Start Here
color 0F

echo.
echo ========================================
echo   Smart Fire Inspector - Start Here
echo ========================================
echo.
echo Vitajte v Smart Fire Inspector publish systeme!
echo.
echo Tento skript vam pomoze spustit spravne menu.
echo.

REM Nastavenie spravnej cesty
cd /d "%~dp0"

echo Aktualna cesta: %CD%
echo.

echo Kontrolujem dostupnost suborov...
echo.

set FILES_OK=1

if exist "MASTER-MENU-FIXED.bat" (
    echo [OK] MASTER-MENU-FIXED.bat
) else (
    echo [CHYBA] MASTER-MENU-FIXED.bat neexistuje
    set FILES_OK=0
)

if exist "01-config" (
    echo [OK] Adresar 01-config
) else (
    echo [CHYBA] Adresar 01-config neexistuje
    set FILES_OK=0
)

if exist "02-publish" (
    echo [OK] Adresar 02-publish
) else (
    echo [CHYBA] Adresar 02-publish neexistuje
    set FILES_OK=0
)

if exist "03-deploy" (
    echo [OK] Adresar 03-deploy
) else (
    echo [CHYBA] Adresar 03-deploy neexistuje
    set FILES_OK=0
)

if exist "04-start" (
    echo [OK] Adresar 04-start
) else (
    echo [CHYBA] Adresar 04-start neexistuje
    set FILES_OK=0
)

echo.

if %FILES_OK%==1 (
    echo Vsetko je pripravene!
    echo.
    echo Vyberte sposob spustenia:
    echo.
    echo [1] Spustit Master Menu (odporucane)
    echo [2] Spustit rychly test
    echo [3] Zobrazit help
    echo [4] Ukoncit
    echo.
    set /p choice="Zadajte volbu (1-4): "
    
    if "!choice!"=="1" (
        echo.
        echo Spustam Master Menu...
        call MASTER-MENU-FIXED.bat
    ) else if "!choice!"=="2" (
        echo.
        echo Spustam rychly test...
        echo Datum: %date% %time%
        echo Cesta: %CD%
        echo Test dokonceny.
        pause
    ) else if "!choice!"=="3" (
        echo.
        echo ========================================
        echo   HELP - Ako pouzivat system
        echo ========================================
        echo.
        echo 1. MASTER-MENU-FIXED.bat - Hlavne menu
        echo 2. 01-config\ - Konfiguracne skripty
        echo 3. 02-publish\ - Publikovacie skripty
        echo 4. 03-deploy\ - Nasadzovacie skripty
        echo 5. 04-start\ - Spustacie skripty
        echo.
        echo Pre viac informacii pozrite README.txt
        echo.
        pause
    ) else (
        echo Ukoncujem...
    )
) else (
    echo.
    echo POZOR: Niektore subory chybaju!
    echo.
    echo Skontrolujte, ci ste v spravnom adresari:
    echo c:\SmartFireInspectorPublish\
    echo.
    echo Ak subory chybaju, spustite znovu setup skripty.
    echo.
    pause
)

echo.
echo Dakujeme za pouzitie Smart Fire Inspector!
echo.
