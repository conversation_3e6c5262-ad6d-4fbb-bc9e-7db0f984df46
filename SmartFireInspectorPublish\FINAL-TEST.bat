@echo off
echo ========================================
echo Smart Fire Inspector - FINAL TEST
echo ========================================
echo.

echo Testujem FINALNE opravene cesty...
echo.

echo Aktualna cesta: %CD%
echo.

echo [1] ZDROJOVE PROJEKTY (musia existovat):
echo.

if exist "c:\SmartFireInspector\backend" (
    echo [OK] Backend: c:\SmartFireInspector\backend
    if exist "c:\SmartFireInspector\backend\Program.cs" (
        echo     [OK] Program.cs existuje
    ) else (
        echo     [CHYBA] Program.cs neexistuje
    )
) else (
    echo [CHYBA] Backend neexistuje: c:\SmartFireInspector\backend
)

if exist "c:\SmartFireInspector\web-app" (
    echo [OK] Web App: c:\SmartFireInspector\web-app
    if exist "c:\SmartFireInspector\web-app\package.json" (
        echo     [OK] package.json existuje
    ) else (
        echo     [CHYBA] package.json neexistuje
    )
) else (
    echo [CHYBA] Web App neexistuje: c:\SmartFireInspector\web-app
)

if exist "c:\SmartFireInspector\mobile-app" (
    echo [OK] Mobile App: c:\SmartFireInspector\mobile-app
    if exist "c:\SmartFireInspector\mobile-app\pubspec.yaml" (
        echo     [OK] pubspec.yaml existuje
    ) else (
        echo     [CHYBA] pubspec.yaml neexistuje
    )
) else (
    echo [CHYBA] Mobile App neexistuje: c:\SmartFireInspector\mobile-app
)

echo.
echo [2] PUBLISH ADRESARE (budu vytvorene):
echo.

set PUBLISH_BASE=c:\SmartFireInspector\SmartFireInspectorPublish\published

if exist "%PUBLISH_BASE%" (
    echo [OK] Publish base existuje: %PUBLISH_BASE%
) else (
    echo [INFO] Publish base neexistuje, bude vytvoreny: %PUBLISH_BASE%
    mkdir "%PUBLISH_BASE%" 2>nul
)

echo.
echo [3] SKRIPTY (musia existovat):
echo.

if exist "01-config\environment-setup.bat" (
    echo [OK] environment-setup.bat
) else (
    echo [CHYBA] environment-setup.bat neexistuje
)

if exist "02-publish\publish-backend.bat" (
    echo [OK] publish-backend.bat
) else (
    echo [CHYBA] publish-backend.bat neexistuje
)

if exist "04-start\start-all-services.bat" (
    echo [OK] start-all-services.bat
) else (
    echo [CHYBA] start-all-services.bat neexistuje
)

echo.
echo [4] TEST SPUSTENIA ENVIRONMENT CHECK:
echo.

echo Spustam environment-setup.bat...
if exist "01-config\environment-setup.bat" (
    call "01-config\environment-setup.bat"
    echo.
    echo Environment check dokonceny.
) else (
    echo CHYBA: environment-setup.bat neexistuje!
)

echo.
echo ========================================
echo FINAL TEST DOKONCENY!
echo ========================================
echo.

echo SPRAVNE CESTY:
echo - Zdrojove projekty: c:\SmartFireInspector\backend, web-app, mobile-app
echo - Publish skripty: c:\SmartFireInspector\SmartFireInspectorPublish\
echo - Publikovane verzie: c:\SmartFireInspector\SmartFireInspectorPublish\published\
echo.

echo Pre spustenie master menu pouzite:
echo START-HERE.bat
echo.

echo Stlacte lubovolnu klavesu...
pause > nul
