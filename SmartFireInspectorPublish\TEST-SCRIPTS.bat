@echo off
echo ========================================
echo Smart Fire Inspector - Test Scripts
echo ========================================
echo.

echo Testujem strukturu adresarov...
echo.

echo [1/4] Kontrolujem konfiguracne skripty...
if exist "01-config\mobile-api-config.bat" (
    echo OK: mobile-api-config.bat
) else (
    echo CHYBA: mobile-api-config.bat neexistuje
)

if exist "01-config\environment-setup.bat" (
    echo OK: environment-setup.bat
) else (
    echo CHYBA: environment-setup.bat neexistuje
)

echo.
echo [2/4] Kontrolujem publikovacie skripty...
if exist "02-publish\publish-all.bat" (
    echo OK: publish-all.bat
) else (
    echo CHYBA: publish-all.bat neexistuje
)

if exist "02-publish\publish-backend.bat" (
    echo OK: publish-backend.bat
) else (
    echo CHYBA: publish-backend.bat neexistuje
)

if exist "02-publish\publish-webapp.bat" (
    echo OK: publish-webapp.bat
) else (
    echo CHYBA: publish-webapp.bat neexistuje
)

if exist "02-publish\build-mobile.bat" (
    echo OK: build-mobile.bat
) else (
    echo CHYBA: build-mobile.bat neexistuje
)

echo.
echo [3/4] Kontrolujem nasadzovacie skripty...
if exist "03-deploy\deploy-mobile-usb.bat" (
    echo OK: deploy-mobile-usb.bat
) else (
    echo CHYBA: deploy-mobile-usb.bat neexistuje
)

if exist "03-deploy\deploy-mobile-emulator.bat" (
    echo OK: deploy-mobile-emulator.bat
) else (
    echo CHYBA: deploy-mobile-emulator.bat neexistuje
)

echo.
echo [4/4] Kontrolujem spustacie skripty...
if exist "04-start\start-all-services.bat" (
    echo OK: start-all-services.bat
) else (
    echo CHYBA: start-all-services.bat neexistuje
)

if exist "04-start\start-backend-dev.bat" (
    echo OK: start-backend-dev.bat
) else (
    echo CHYBA: start-backend-dev.bat neexistuje
)

if exist "04-start\start-webapp-dev.bat" (
    echo OK: start-webapp-dev.bat
) else (
    echo CHYBA: start-webapp-dev.bat neexistuje
)

if exist "04-start\start-mobile-dev.bat" (
    echo OK: start-mobile-dev.bat
) else (
    echo CHYBA: start-mobile-dev.bat neexistuje
)

echo.
echo [BONUS] Kontrolujem zdrojove adresare...
if exist "c:\SmartFireInspector\backend" (
    echo OK: Backend adresar existuje
) else (
    echo POZOR: Backend adresar neexistuje
)

if exist "c:\SmartFireInspector\web-app" (
    echo OK: Web app adresar existuje
) else (
    echo POZOR: Web app adresar neexistuje
)

if exist "c:\SmartFireInspector\mobile-app" (
    echo OK: Mobile app adresar existuje
) else (
    echo POZOR: Mobile app adresar neexistuje
)

echo.
echo ========================================
echo TEST DOKONCENY!
echo ========================================
echo.
echo Vsetky skripty su vytvorene a pripravene na pouzitie.
echo.
echo Pre spustenie pouzite:
echo - MASTER-MENU.bat (hlavne menu)
echo - Alebo jednotlive skripty v prislusnych adresaroch
echo.
echo Struktura:
echo 01-config/    - Konfiguracia
echo 02-publish/   - Publikovanie
echo 03-deploy/    - Nasadenie
echo 04-start/     - Spustenie
echo.
pause
