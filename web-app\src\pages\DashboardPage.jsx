import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import Header from '../components/common/Header';

const DashboardPage = () => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Dashboard
              </h2>
              <p className="text-gray-600 mb-4">
                Vitajte v systéme Smart Fire Inspector!
              </p>
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Informácie o používateľovi:
                </h3>
                <div className="text-left space-y-2">
                  <p><strong>Email:</strong> {user?.email}</p>
                  <p><strong>Meno:</strong> {user?.firstName} {user?.lastName}</p>
                  <p><strong>Rola:</strong> {user?.role}</p>
                  <p><strong>ID:</strong> {user?.id}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
