import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const DashboardPage = () => {
  const { user, logout } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                Smart Fire Inspector
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">
                Vitaj<PERSON>, {user?.firstName} {user?.lastName}
              </span>
              <span className="text-sm text-gray-500">
                ({user?.role})
              </span>
              <button
                onClick={logout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Odh<PERSON>ás<PERSON>ť sa
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Dashboard
              </h2>
              <p className="text-gray-600 mb-4">
                Vitajte v systéme Smart Fire Inspector!
              </p>
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Informácie o používateľovi:
                </h3>
                <div className="text-left space-y-2">
                  <p><strong>Email:</strong> {user?.email}</p>
                  <p><strong>Meno:</strong> {user?.firstName} {user?.lastName}</p>
                  <p><strong>Rola:</strong> {user?.role}</p>
                  <p><strong>ID:</strong> {user?.id}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
