Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmartFireInspector.Api", "backend\SmartFireInspector.Api.csproj", "{BB4D40D6-5DC3-FA60-CC6D-E6D4B638A9E6}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{BB4D40D6-5DC3-FA60-CC6D-E6D4B638A9E6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB4D40D6-5DC3-FA60-CC6D-E6D4B638A9E6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB4D40D6-5DC3-FA60-CC6D-E6D4B638A9E6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB4D40D6-5DC3-FA60-CC6D-E6D4B638A9E6}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {68783E14-C0A4-4582-80E6-1B59D383612A}
	EndGlobalSection
EndGlobal
