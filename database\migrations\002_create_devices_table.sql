-- Vyt<PERSON>enie tabuľky zariadení
CREATE TABLE IF NOT EXISTS devices (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    location VARCHAR(255) NOT NULL,
    serial_number VARCHAR(100) UNIQUE,
    last_inspection_date DATE,
    next_inspection_date DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'Active',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexy pre výkon
CREATE INDEX IF NOT EXISTS idx_devices_type ON devices(type);
CREATE INDEX IF NOT EXISTS idx_devices_location ON devices(location);
CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status);
CREATE INDEX IF NOT EXISTS idx_devices_serial_number ON devices(serial_number);
CREATE INDEX IF NOT EXISTS idx_devices_next_inspection ON devices(next_inspection_date);

-- <PERSON><PERSON> pre automatické aktualizovanie updated_at
CREATE TRIGGER update_devices_updated_at 
    BEFORE UPDATE ON devices 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
