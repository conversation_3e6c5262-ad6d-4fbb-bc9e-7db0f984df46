// Date formatting helpers
export const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('sk-SK');
};

export const formatDateTime = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleString('sk-SK');
};

// Validation helpers
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password) => {
  return password && password.length >= 6;
};

// String helpers
export const capitalize = (str) => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export const truncateText = (text, maxLength = 100) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Role helpers
export const getRoleDisplayName = (role) => {
  const roleNames = {
    'Admin': 'Administrátor',
    'Inspector': 'Kontrolór',
    'User': 'Používateľ'
  };
  return roleNames[role] || role;
};

// Error handling helpers
export const getErrorMessage = (error) => {
  if (typeof error === 'string') return error;
  if (error?.response?.data?.message) return error.response.data.message;
  if (error?.message) return error.message;
  return 'Neočakávaná chyba';
};
