@echo off
echo ========================================
echo FINAL BACKEND TEST - Smart Fire Inspector
echo ========================================
echo.

echo Tento test overi, ze start-backend-dev.bat funguje spravne.
echo.

echo [1] Ukoncujem vsetky existujuce backend procesy...
taskkill /F /IM SmartFireInspector.Api.exe /T >nul 2>&1
taskkill /F /IM dotnet.exe /T >nul 2>&1
echo Procesy ukoncene.

echo.
echo [2] Cakam 5 sekund...
ping 127.0.0.1 -n 6 >nul

echo.
echo [3] Kontrolujem, ze port 5000 je volny...
netstat -ano | findstr :5000 > port_check.tmp
if exist port_check.tmp (
    echo [POZOR] Port 5000 je stale obsadeny:
    type port_check.tmp
    del port_check.tmp
    echo Ukoncujem procesy na porte 5000...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000') do (
        taskkill /F /PID %%a >nul 2>&1
    )
) else (
    echo [OK] Port 5000 je volny
)

echo.
echo [4] Spustam start-backend-dev.bat na pozadi...
start /MIN "Backend Test" cmd /c "cd /d c:\SmartFireInspector\SmartFireInspectorPublish\04-start && start-backend-dev.bat"

echo.
echo [5] Cakam 15 sekund na spustenie backendu...
echo Cakam...
ping 127.0.0.1 -n 16 >nul

echo.
echo [6] Testujem, ci backend bezi...

echo Kontrolujem port 5000...
netstat -ano | findstr :5000 > port_check.tmp
if exist port_check.tmp (
    echo [OK] Port 5000 je obsadeny - backend bezi
    del port_check.tmp
) else (
    echo [CHYBA] Port 5000 nie je obsadeny - backend nebezi
    goto :test_failed
)

echo.
echo [7] Testujem API endpoint...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/auth/test-accounts' -UseBasicParsing -TimeoutSec 10; Write-Host '[SUCCESS] API endpoint funguje!'; Write-Host 'Status:' $response.StatusCode; $json = $response.Content | ConvertFrom-Json; Write-Host 'Test accounts:' $json.accounts.Length } catch { Write-Host '[ERROR] API endpoint nefunguje:' $_.Exception.Message; exit 1 }"

if %ERRORLEVEL% neq 0 goto :test_failed

echo.
echo [8] Testujem Swagger UI...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/swagger' -UseBasicParsing -TimeoutSec 10; Write-Host '[SUCCESS] Swagger UI je dostupny'; Write-Host 'Status:' $response.StatusCode } catch { Write-Host '[WARNING] Swagger UI nie je dostupny:' $_.Exception.Message }"

echo.
echo ========================================
echo TEST USPESNY!
echo ========================================
echo.

echo VYSLEDKY:
echo [OK] start-backend-dev.bat funguje spravne
echo [OK] Backend API bezi na porte 5000
echo [OK] API endpoint /api/auth/test-accounts funguje
echo [OK] Backend odpovedal na HTTP requesty
echo.

echo DOSTUPNE ENDPOINTY:
echo - http://localhost:5000/api/auth/test-accounts
echo - http://localhost:5000/swagger
echo.

echo Backend je pripraveny na pouzitie!
echo.

echo Pre ukoncenie backendu zatvorte okno alebo stlacte Ctrl+C.
echo.
goto :end

:test_failed
echo.
echo ========================================
echo TEST ZLYHAL!
echo ========================================
echo.

echo PROBLEM: Backend sa nepodarilo spustit alebo nefunguje spravne.
echo.

echo MOZNE PRICINY:
echo 1. .NET 8 SDK nie je nainstalovany
echo 2. Port 5000 je obsadeny inym procesom
echo 3. Chyby v backend kode
echo 4. Nedostatocne prava
echo.

echo RIESENIA:
echo 1. Nainstalujte .NET 8 SDK
echo 2. Restartujte pocitac
echo 3. Spustite ako administrator
echo 4. Skontrolujte backend kod v Visual Studio
echo.

echo Pre manualne testovanie:
echo cd c:\SmartFireInspector\backend
echo dotnet run --urls=http://0.0.0.0:5000
echo.

:end
echo Stlacte lubovolnu klavesu...
pause > nul
