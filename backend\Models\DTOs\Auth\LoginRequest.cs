using System.ComponentModel.DataAnnotations;

namespace SmartFireInspector.Api.Models.DTOs.Auth;

public class LoginRequest
{
    [Required(ErrorMessage = "Email je povinný")]
    [EmailAddress(ErrorMessage = "Neplatný formát emailu")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Heslo je povinné")]
    [MinLength(6, ErrorMessage = "Heslo musí mať aspoň 6 znakov")]
    public string Password { get; set; } = string.Empty;
}
