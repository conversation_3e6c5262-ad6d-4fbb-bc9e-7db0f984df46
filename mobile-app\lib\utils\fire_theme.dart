import 'package:flutter/material.dart';

/// Protipožiarna farebná paleta pre Smart Fire Inspector
class FireTheme {
  // Základné protipožiarne farby
  static const Color fireRed = Color(0xFFD32F2F);           // Hlavná červená - hasičské vozidlá
  static const Color emergencyOrange = Color(0xFFFF6F00);   // Núdzová oranžová - výstražné signály
  static const Color safetyYellow = Color(0xFFFFD600);      // Bezpečnostná žltá - výstražné zna<PERSON>ky
  static const Color hydrantBlue = Color(0xFF1976D2);       // Modrá hydrantov
  static const Color extinguisherGreen = Color(0xFF388E3C); // <PERSON><PERSON><PERSON> hasiacich prístrojov
  static const Color smokeGray = Color(0xFF616161);         // Sivá dymu/kovu
  static const Color alarmRed = Color(0xFFB71C1C);          // Tmavá červená alarmov
  
  // Doplnkové farby
  static const Color lightSmoke = Color(0xFFEEEEEE);        // Svetlá sivá pre pozadie
  static const Color darkMetal = Color(0xFF424242);         // Tmavá kovová
  static const Color warningAmber = Color(0xFFFFC107);      // Jantárová pre upozornenia
  
  // Gradient farby pre pozadie
  static const LinearGradient fireGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFD32F2F),
      Color(0xFFB71C1C),
    ],
  );
  
  static const LinearGradient emergencyGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFF6F00),
      Color(0xFFE65100),
    ],
  );
  
  // Funkčné farby pre rôzne typy zariadení
  static const Map<String, Color> deviceColors = {
    'fire_extinguisher': extinguisherGreen,
    'hydrant': hydrantBlue,
    'alarm': alarmRed,
    'emergency_exit': safetyYellow,
    'smoke_detector': smokeGray,
    'sprinkler': hydrantBlue,
    'fire_hose': fireRed,
    'emergency_light': warningAmber,
  };
  
  // Farby pre stavy kontrol
  static const Map<String, Color> inspectionStatusColors = {
    'passed': Color(0xFF4CAF50),      // Zelená - prešlo
    'failed': Color(0xFFD32F2F),      // Červená - neprešlo
    'warning': Color(0xFFFF9800),     // Oranžová - upozornenie
    'pending': Color(0xFF9E9E9E),     // Sivá - čaká
    'overdue': Color(0xFFB71C1C),     // Tmavá červená - po termíne
  };
  
  // Farby pre priority
  static const Map<String, Color> priorityColors = {
    'critical': Color(0xFFB71C1C),    // Kritická - tmavá červená
    'high': Color(0xFFD32F2F),        // Vysoká - červená
    'medium': Color(0xFFFF9800),      // Stredná - oranžová
    'low': Color(0xFF4CAF50),         // Nízka - zelená
  };
  
  // Metóda pre získanie farby zariadenia
  static Color getDeviceColor(String deviceType) {
    return deviceColors[deviceType] ?? smokeGray;
  }
  
  // Metóda pre získanie farby stavu kontroly
  static Color getInspectionStatusColor(String status) {
    return inspectionStatusColors[status] ?? smokeGray;
  }
  
  // Metóda pre získanie farby priority
  static Color getPriorityColor(String priority) {
    return priorityColors[priority] ?? smokeGray;
  }
  
  // Predvolené farby pre akčné tlačidlá
  static const Map<String, Color> actionColors = {
    'camera': Color(0xFF6A1B9A),      // Fialová pre fotoaparát
    'qr_scanner': Color(0xFF303F9F),  // Indigo pre QR skener
    'devices': emergencyOrange,       // Oranžová pre zariadenia
    'inspections': extinguisherGreen, // Zelená pre kontroly
    'reports': hydrantBlue,           // Modrá pre reporty
    'settings': smokeGray,            // Sivá pre nastavenia
  };
  
  // Metóda pre získanie farby akcie
  static Color getActionColor(String action) {
    return actionColors[action] ?? smokeGray;
  }
}
