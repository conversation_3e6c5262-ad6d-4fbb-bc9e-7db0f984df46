import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

const Header = () => {
  const { user, logout } = useAuth();

  return (
    <nav className="bg-white shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-900">
              Smart Fire Inspector
            </h1>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">
              <PERSON><PERSON><PERSON>, {user?.firstName} {user?.lastName}
            </span>
            <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {user?.role}
            </span>
            <button
              onClick={logout}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Odhlásiť sa
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Header;
