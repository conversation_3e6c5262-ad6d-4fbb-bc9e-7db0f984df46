import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import '../utils/constants.dart';
import 'storage_service.dart';

class ApiService {
  static const Duration _timeout = Duration(seconds: 10);

  static Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static Future<Map<String, String>> get _authHeaders async {
    final token = await StorageService.getToken();
    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  static Future<http.Response> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    bool requiresAuth = false,
  }) async {
    final url = Uri.parse('${Constants.apiBaseUrl}$endpoint');
    final headers = requiresAuth ? await _authHeaders : _headers;

    print('🌐 API Request: $method $url');
    print('📋 Headers: $headers');
    if (body != null) {
      print('📤 Request body: ${jsonEncode(body)}');
    }

    http.Response response;

    try {
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(url, headers: headers).timeout(_timeout);
          break;
        case 'POST':
          response = await http.post(
            url,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(_timeout);
          break;
        case 'PUT':
          response = await http.put(
            url,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(_timeout);
          break;
        case 'DELETE':
          response = await http.delete(url, headers: headers).timeout(_timeout);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      // Handle 401 Unauthorized
      if (response.statusCode == 401) {
        await StorageService.clearAuthData();
        throw Exception('Session expired. Please login again.');
      }

      return response;
    } on SocketException {
      throw Exception('No internet connection');
    } on HttpException {
      throw Exception('HTTP error occurred');
    } catch (e) {
      print('❌ Request failed: $e');
      print('❌ URL was: $url');
      throw Exception('Request failed: $e');
    }
  }

  // Auth endpoints
  static Future<AuthResponse> login(String email, String password) async {
    final response = await _makeRequest(
      'POST',
      Constants.loginEndpoint,
      body: {
        'email': email,
        'password': password,
      },
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return AuthResponse.fromJson(data);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['message'] ?? 'Login failed');
    }
  }

  static Future<Map<String, dynamic>> getTestAccounts() async {
    final response = await _makeRequest('GET', Constants.testAccountsEndpoint);

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to fetch test accounts');
    }
  }

  // User endpoints
  static Future<User> getProfile() async {
    final response = await _makeRequest(
      'GET',
      Constants.profileEndpoint,
      requiresAuth: true,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return User.fromJson(data);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['message'] ?? 'Failed to fetch profile');
    }
  }
}
