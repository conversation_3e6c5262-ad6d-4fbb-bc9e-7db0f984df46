using SmartFireInspector.Api.Models.Entities;

namespace SmartFireInspector.Api.Services;

public interface IUserService
{
    Task<User?> GetUserByIdAsync(int id);
    Task<User?> GetUserByEmailAsync(string email);
    Task<IEnumerable<User>> GetAllUsersAsync();
}

public class UserService : IUserService
{
    private readonly ILogger<UserService> _logger;
    
    // Fejkové kontá na testovanie (rovnaké ako v AuthService)
    private readonly List<User> _fakeUsers = new()
    {
        new User
        {
            Id = 1,
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
            FirstName = "Admin",
            LastName = "Používateľ",
            Role = "Admin",
            IsActive = true
        },
        new User
        {
            Id = 2,
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("inspector123"),
            FirstName = "J<PERSON>",
            LastName = "Kontrolór",
            Role = "Inspector",
            IsActive = true
        },
        new User
        {
            Id = 3,
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("user123"),
            FirstName = "Peter",
            LastName = "Používateľ",
            Role = "User",
            IsActive = true
        }
    };

    public UserService(ILogger<UserService> logger)
    {
        _logger = logger;
    }

    public async Task<User?> GetUserByIdAsync(int id)
    {
        await Task.Delay(50); // Simulácia async operácie
        return _fakeUsers.FirstOrDefault(u => u.Id == id && u.IsActive);
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        await Task.Delay(50); // Simulácia async operácie
        return _fakeUsers.FirstOrDefault(u => u.Email.ToLower() == email.ToLower() && u.IsActive);
    }

    public async Task<IEnumerable<User>> GetAllUsersAsync()
    {
        await Task.Delay(100); // Simulácia async operácie
        return _fakeUsers.Where(u => u.IsActive);
    }
}
