@echo off
echo ========================================
echo Smart Fire Inspector - Start All Services
echo ========================================
echo.

echo Tento skript spusti vsetky sluzby pre development:
echo 1. Backend API Server (port 5000)
echo 2. Web Application Dev Server (port 3000)
echo 3. Mobile Application v emulatore
echo.

set BACKEND_DIR=c:\SmartFireInspector\backend
set WEBAPP_DIR=c:\SmartFireInspector\web-app
set MOBILE_DIR=c:\SmartFireInspector\mobile-app

echo Kontrolujem adresare...
if not exist "%BACKEND_DIR%" (
    echo CHYBA: Backend adresar neexistuje: %BACKEND_DIR%
    pause
    exit /b 1
)
if not exist "%WEBAPP_DIR%" (
    echo CHYBA: Web app adresar neexistuje: %WEBAPP_DIR%
    pause
    exit /b 1
)
if not exist "%MOBILE_DIR%" (
    echo CHYBA: Mobile app adresar neexistuje: %MOBILE_DIR%
    pause
    exit /b 1
)

echo.
echo Vyberte co chcete spustit:
echo 1. Vsetko (Backend + Web + Mobile)
echo 2. Len Backend API
echo 3. Len Web Application
echo 4. Backend + Web (bez Mobile)
echo 5. Len Mobile Application
echo.
set /p choice="Zadajte volbu (1-5): "

if "%choice%"=="1" (
    set START_BACKEND=1
    set START_WEB=1
    set START_MOBILE=1
) else if "%choice%"=="2" (
    set START_BACKEND=1
    set START_WEB=0
    set START_MOBILE=0
) else if "%choice%"=="3" (
    set START_BACKEND=0
    set START_WEB=1
    set START_MOBILE=0
) else if "%choice%"=="4" (
    set START_BACKEND=1
    set START_WEB=1
    set START_MOBILE=0
) else if "%choice%"=="5" (
    set START_BACKEND=0
    set START_WEB=0
    set START_MOBILE=1
) else (
    echo Neplatna volba!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Spustam sluzby...
echo ========================================

if "%START_BACKEND%"=="1" (
    echo.
    echo [1] Spustam Backend API Server...
    start "Smart Fire Inspector - Backend API" cmd /k "cd /d %BACKEND_DIR% && dotnet run --urls=http://0.0.0.0:5000"
    echo Backend API sa spusta na http://localhost:5000
    timeout /t 3 /nobreak > nul
)

if "%START_WEB%"=="1" (
    echo.
    echo [2] Spustam Web Application Dev Server...
    start "Smart Fire Inspector - Web App" cmd /k "cd /d %WEBAPP_DIR% && npm run dev"
    echo Web App sa spusta na http://localhost:3000
    timeout /t 3 /nobreak > nul
)

if "%START_MOBILE%"=="1" (
    echo.
    echo [3] Pripravujem Mobile Application...
    
    echo Kontrolujem dostupne emulatory...
    call C:\Android\android-sdk\emulator\emulator.exe -list-avds
    
    echo.
    echo Vyberte emulator:
    echo 1. Pixel 9 Pro
    echo 2. Pixel Tablet API 34
    echo 3. Preskocit (emulator uz bezi)
    echo.
    set /p emulator_choice="Zadajte volbu (1-3): "
    
    if "!emulator_choice!"=="1" (
        echo Spustam Pixel 9 Pro emulator...
        start "Android Emulator - Pixel 9 Pro" C:\Android\android-sdk\emulator\emulator.exe -avd Pixel_9_Pro
        timeout /t 15 /nobreak > nul
    ) else if "!emulator_choice!"=="2" (
        echo Spustam Pixel Tablet emulator...
        start "Android Emulator - Pixel Tablet" C:\Android\android-sdk\emulator\emulator.exe -avd Pixel_Tablet_API_34
        timeout /t 15 /nobreak > nul
    )
    
    echo.
    echo Spustam Flutter aplikaciu...
    start "Smart Fire Inspector - Mobile App" cmd /k "cd /d %MOBILE_DIR% && C:\flutter\bin\flutter.bat run"
    echo Mobile App sa spusta v emulatore
)

echo.
echo ========================================
echo USPECH: Sluzby spustene!
echo ========================================
echo.

if "%START_BACKEND%"=="1" (
    echo Backend API: http://localhost:5000
    echo - Swagger UI: http://localhost:5000/swagger (ak je nakonfigurovane)
    echo - Test endpoint: http://localhost:5000/api/auth/test-accounts
)

if "%START_WEB%"=="1" (
    echo.
    echo Web Application: http://localhost:3000
    echo - Automaticky sa otvorit v prehliadaci
)

if "%START_MOBILE%"=="1" (
    echo.
    echo Mobile Application: Android Emulator
    echo - Flutter DevTools budu dostupne po spusteni
    echo - Hot reload: stlacte 'r' v mobile app terminali
)

echo.
echo POZNAMKY:
echo - Vsetky sluzby bezia v samostatnych oknach
echo - Pre ukoncenie zatvorte prislusne okna alebo stlacte Ctrl+C
echo - Pre zmeny v kode pouzite hot reload (web a mobile)
echo - Backend sa automaticky restartuje pri zmenach (ak je nakonfigurovany)
echo.

if "%START_WEB%"=="1" (
    echo Otvariam web aplikaciu v prehliadaci...
    timeout /t 5 /nobreak > nul
    start http://localhost:3000
)

echo.
echo Vsetky sluzby su spustene a pripravene na pouzitie!
pause
