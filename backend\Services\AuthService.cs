using SmartFireInspector.Api.Models.DTOs.Auth;
using SmartFireInspector.Api.Models.Entities;

namespace SmartFireInspector.Api.Services;

public interface IAuthService
{
    Task<AuthResponse> LoginAsync(LoginRequest request);
    Task<User?> GetUserByEmailAsync(string email);
}

public class AuthService : IAuthService
{
    private readonly IJwtService _jwtService;
    
    // Fejkové kontá na testovanie
    private readonly List<User> _fakeUsers = new()
    {
        new User
        {
            Id = 1,
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
            FirstName = "Admin",
            LastName = "Používateľ",
            Role = "Admin",
            IsActive = true
        },
        new User
        {
            Id = 2,
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("inspector123"),
            FirstName = "J<PERSON>",
            LastName = "Kontrolór",
            Role = "Inspector",
            IsActive = true
        },
        new User
        {
            Id = 3,
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("user123"),
            FirstName = "Peter",
            LastName = "Používateľ",
            Role = "User",
            IsActive = true
        }
    };

    public AuthService(IJwtService jwtService)
    {
        _jwtService = jwtService;
    }

    public async Task<AuthResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            // Simulácia async operácie
            await Task.Delay(100);
            
            var user = _fakeUsers.FirstOrDefault(u => u.Email.ToLower() == request.Email.ToLower());
            
            if (user == null || !user.IsActive)
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "Neplatné prihlasovacie údaje"
                };
            }

            if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "Neplatné prihlasovacie údaje"
                };
            }

            var token = _jwtService.GenerateToken(user);

            return new AuthResponse
            {
                Success = true,
                Message = "Prihlásenie úspešné",
                Token = token,
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Role = user.Role
                }
            };
        }
        catch (Exception ex)
        {
            return new AuthResponse
            {
                Success = false,
                Message = "Chyba pri prihlasovaní: " + ex.Message
            };
        }
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        await Task.Delay(50); // Simulácia async operácie
        return _fakeUsers.FirstOrDefault(u => u.Email.ToLower() == email.ToLower());
    }
}
