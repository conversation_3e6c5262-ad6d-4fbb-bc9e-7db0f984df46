@echo off
echo ========================================
echo Smart Fire Inspector - Start Backend (Development)
echo ========================================
echo.

set BACKEND_DIR=c:\SmartFireInspector\SmartFireInspectorPublish\backend

echo Kontrolujem backend adresar...
if not exist "%BACKEND_DIR%" (
    echo CHYBA: Backend adresar neexistuje: %BACKEND_DIR%
    pause
    exit /b 1
)

echo Spustam Backend API v development mode...
echo.
echo API bude dostupne na:
echo - http://localhost:5000
echo - http://0.0.0.0:5000 (pre pristup z mobilnych zariadeni)
echo.
echo Pre ukoncenie stlacte Ctrl+C
echo.

cd /d "%BACKEND_DIR%"
dotnet run --urls=http://0.0.0.0:5000

pause
