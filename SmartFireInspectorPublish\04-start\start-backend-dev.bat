@echo off
echo ========================================
echo Smart Fire Inspector - Start Backend (Development)
echo ========================================
echo.

set BACKEND_DIR=c:\SmartFireInspector\backend

echo Kontrolujem backend adresar...
if not exist "%BACKEND_DIR%" (
    echo CHYBA: Backend adresar neexistuje: %BACKEND_DIR%
    pause
    exit /b 1
)

echo.
echo [1/3] Kontrolujem beziacich procesov...
echo Hladam beziacich SmartFireInspector.Api procesov...

REM Ukoncenie beziacich procesov
tasklist /FI "IMAGENAME eq SmartFireInspector.Api.exe" 2>NUL | find /I /N "SmartFireInspector.Api.exe" > NUL
if "%ERRORLEVEL%"=="0" (
    echo Najdeny bezi proces SmartFireInspector.Api.exe, ukoncujem...
    taskkill /F /IM SmartFireInspector.Api.exe /T > NUL 2>&1
    timeout /t 2 /nobreak > NUL
    echo Proces ukonceny.
) else (
    echo Ziadny bezi proces nenajdeny.
)

REM Kontrola portov
echo Kontrolujem port 5000...
netstat -ano | findstr :5000 > port_check.tmp
if exist port_check.tmp (
    for /f "tokens=5" %%a in (port_check.tmp) do (
        echo Ukoncujem proces na porte 5000 (PID: %%a)...
        taskkill /F /PID %%a > NUL 2>&1
    )
    del port_check.tmp
    timeout /t 2 /nobreak > NUL
)

echo.
echo [2/3] Cistenie build cache...
cd /d "%BACKEND_DIR%"

echo Spustam dotnet clean...
dotnet clean > NUL 2>&1

REM Pokus o vymazanie bin a obj adresarov
if exist "bin" (
    echo Mazem bin adresar...
    rmdir /s /q bin 2>NUL
    if exist "bin" (
        echo POZOR: bin adresar sa nepodarilo vymazat uplne, pokracujem...
    )
)

if exist "obj" (
    echo Mazem obj adresar...
    rmdir /s /q obj 2>NUL
    if exist "obj" (
        echo POZOR: obj adresar sa nepodarilo vymazat uplne, pokracujem...
    )
)

echo.
echo [3/3] Spustam Backend API v development mode...
echo.

REM Najprv skusime build
echo Spustam dotnet build...
dotnet build
if %ERRORLEVEL% neq 0 (
    echo.
    echo CHYBA: Build zlyhal! Skusam restore a build znovu...
    dotnet restore
    dotnet build
    if %ERRORLEVEL% neq 0 (
        echo.
        echo CHYBA: Build stale zlyha! Skontrolujte kod.
        goto :error_exit
    )
)

echo.
echo Build uspesny, spustam aplikaciu...
echo.
echo API bude dostupne na:
echo - http://localhost:5000
echo - http://0.0.0.0:5000 (pre pristup z mobilnych zariadeni)
echo.
echo Pre ukoncenie stlacte Ctrl+C
echo.

dotnet run --urls=http://0.0.0.0:5000

if %ERRORLEVEL% neq 0 (
    goto :error_exit
)

echo.
echo Backend API sa uspesne spustil!
echo.
goto :end

:error_exit
echo.
echo ========================================
echo CHYBA: Backend API sa nepodarilo spustit!
echo ========================================
echo.
echo Mozne priciny:
echo 1. .NET 8 SDK nie je nainstalovany
echo 2. Chyby v kode
echo 3. Chybaju dependencies
echo 4. Port 5000 je stale obsadeny
echo 5. Subory su zamknute inym procesom
echo.
echo Riesenia:
echo 1. Nainstalujte .NET 8 SDK
echo 2. Skontrolujte kod v Visual Studio
echo 3. Spustite: cleanup-backend.bat
echo 4. Restartujte pocitac
echo 5. Spustite: dotnet restore
echo.
echo Pre detailne chyby spustite manualne:
echo cd %BACKEND_DIR%
echo dotnet clean
echo dotnet restore
echo dotnet run --urls=http://0.0.0.0:5000
echo.

:end
pause
