@echo off
echo ========================================
echo Smart Fire Inspector - Start Backend API
echo ========================================
echo.

set BACKEND_DIR=c:\SmartFireInspector\backend

echo Kontrolujem backend adresar...
if not exist "%BACKEND_DIR%" (
    echo CHYBA: Backend adresar neexistuje: %BACKEND_DIR%
    pause
    exit /b 1
)

echo.
echo Ukoncujem existujuce procesy...
taskkill /F /IM SmartFireInspector.Api.exe /T >nul 2>&1
taskkill /F /IM dotnet.exe /T >nul 2>&1

echo Cakam 3 sekundy...
ping 127.0.0.1 -n 4 >nul

echo.
echo Prechadzam do backend adresara...
cd /d "%BACKEND_DIR%"

echo.
echo Cistim build cache...
if exist "bin" rmdir /s /q "bin" >nul 2>&1
if exist "obj" rmdir /s /q "obj" >nul 2>&1

echo.
echo Spustam dotnet restore...
dotnet restore

echo.
echo Spustam Backend API...
echo.
echo API bude dostupne na:
echo - http://localhost:5000
echo - http://0.0.0.0:5000
echo.
echo Pre ukoncenie stlacte Ctrl+C
echo.

dotnet run --urls=http://0.0.0.0:5000

echo.
echo Ak sa backend nepodarilo spustit, skuste:
echo 1. Restartovat pocitac
echo 2. Spustit ako administrator
echo 3. Skontrolovat .NET 8 SDK: dotnet --version
echo.
pause
