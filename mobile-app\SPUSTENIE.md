# Spustenie Flutter aplikácie Smart Fire Inspector

## Požiadavky

### 1. Flutter SDK
Stiahnite a nainštalujte Flutter SDK:
- **Windows**: https://docs.flutter.dev/get-started/install/windows
- **macOS**: https://docs.flutter.dev/get-started/install/macos
- **Linux**: https://docs.flutter.dev/get-started/install/linux

### 2. Android Studio alebo VS Code
- **Android Studio**: https://developer.android.com/studio
- **VS Code**: https://code.visualstudio.com/ + Flutter extension

### 3. Android emulator alebo fyzické zariadenie
- Android emulator cez Android Studio
- Alebo pripojené Android/iOS zariadenie s povoleným USB debugging

## Spustenie

### 1. Overenie Flutter inštalácie
```bash
flutter doctor
```

### 2. Inštalácia závislostí
```bash
cd mobile-app
flutter pub get
```

### 3. Spustenie aplikácie
```bash
# Pre Android emulator
flutter run

# Pre konkrétne zariadenie
flutter devices
flutter run -d <device_id>

# Pre release build
flutter run --release
```

## Konfigurácia API

### Pre Android emulator
API endpoint je nastavený na `http://********:5000/api` (localhost cez emulator)

### Pre fyzické zariadenie
Zmeňte v `lib/utils/constants.dart`:
```dart
static const String apiBaseUrl = 'http://YOUR_COMPUTER_IP:5000/api';
```

Kde `YOUR_COMPUTER_IP` je IP adresa vášho počítača v lokálnej sieti.

## Testovanie offline režimu

### 1. Prvé prihlásenie (online)
- Spustite .NET API: `cd backend && dotnet run`
- Prihláste sa s testovým kontom
- Konto sa uloží pre offline prístup

### 2. Testovanie offline
- Vypnite WiFi/mobilné dáta na zariadení
- Aplikácia zobrazí orange "Offline režim" banner
- Prihláste sa s uloženým kontom

### 3. Testové kontá
- **<EMAIL>** / admin123
- **<EMAIL>** / inspector123
- **<EMAIL>** / user123

## Riešenie problémov

### Gradle build chyby
```bash
cd android
./gradlew clean
cd ..
flutter clean
flutter pub get
```

### Connectivity plugin chyby
Pridajte do `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### iOS build chyby
```bash
cd ios
pod install
cd ..
flutter clean
flutter pub get
```

## Funkcie aplikácie

### ✅ Implementované
- Prihlasovanie s JWT autentifikáciou
- Dashboard s informáciami o používateľovi
- Offline režim s lokálnou autentifikáciou
- Connectivity monitoring
- Material Design UI
- Secure storage pre tokeny

### 🚧 Pripravené na rozšírenie
- CRUD operácie pre zariadenia
- Fotenie zariadení
- Offline data synchronizácia
- Push notifikácie

## Architektúra

```
Flutter App → .NET Core API (port 5000) → Supabase DB
     ↓
Offline Storage (SharedPreferences + Secure Storage)
```

## Logs a debugging

### Flutter logs
```bash
flutter logs
```

### Android logs
```bash
adb logcat
```

### iOS logs
```bash
# V Xcode alebo
flutter logs
```
