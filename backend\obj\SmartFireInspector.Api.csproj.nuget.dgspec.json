{"format": 1, "restore": {"C:\\SmartFireInspector\\backend\\SmartFireInspector.Api.csproj": {}}, "projects": {"C:\\SmartFireInspector\\backend\\SmartFireInspector.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\SmartFireInspector\\backend\\SmartFireInspector.Api.csproj", "projectName": "SmartFireInspector.Api", "projectPath": "C:\\SmartFireInspector\\backend\\SmartFireInspector.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\SmartFireInspector\\backend\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Dapper": {"target": "Package", "version": "[2.1.24, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Npgsql": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}}