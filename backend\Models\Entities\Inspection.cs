namespace SmartFireInspector.Api.Models.Entities;

public class Inspection
{
    public int Id { get; set; }
    public int DeviceId { get; set; }
    public int UserId { get; set; }
    public DateTime InspectionDate { get; set; }
    public string Status { get; set; } = string.Empty; // Passed, Failed, Pending
    public string? Notes { get; set; }
    public string? Recommendations { get; set; }
    public DateTime? NextInspectionDate { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public Device? Device { get; set; }
    public User? User { get; set; }
    public List<InspectionImage> Images { get; set; } = new();
}
