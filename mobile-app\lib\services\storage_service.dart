import 'dart:convert';
import 'package:flutter/foundation.dart';
// import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class StorageService {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();

      // Test čítania/zápisu pre overenie funkčnosti
      await _prefs!.setString('_test_key', 'test_value');
      final testValue = _prefs!.getString('_test_key');
      if (testValue != 'test_value') {
        throw Exception('SharedPreferences test failed');
      }
      await _prefs!.remove('_test_key');

    } catch (e) {
      if (kDebugMode) {
        debugPrint('StorageService: Chyba pri inicializácii: $e');
      }
      // Retry s dlhším timeout
      try {
        await Future.delayed(const Duration(milliseconds: 500));
        _prefs = await SharedPreferences.getInstance();
      } catch (e2) {
        if (kDebugMode) {
          debugPrint('StorageService: Druhý pokus zlyhal: $e2');
        }
        rethrow;
      }
    }
  }

  // Token management (using shared preferences for web compatibility)
  static Future<void> setToken(String token) async {
    if (_prefs == null) await init();
    await _prefs?.setString(Constants.tokenKey, token);
  }

  static Future<String?> getToken() async {
    try {
      if (_prefs == null) await init();
      return _prefs?.getString(Constants.tokenKey);
    } catch (e) {
      debugPrint('StorageService: Chyba pri čítaní tokenu: $e');
      return null;
    }
  }

  static Future<void> removeToken() async {
    await _prefs?.remove(Constants.tokenKey);
  }

  // User management (shared preferences)
  static Future<void> setUser(User user) async {
    if (_prefs == null) await init();
    final userJson = jsonEncode(user.toJson());
    await _prefs?.setString(Constants.userKey, userJson);
  }

  static Future<User?> getUser() async {
    try {
      if (_prefs == null) await init();
      final userJson = _prefs?.getString(Constants.userKey);
      if (userJson != null && userJson.isNotEmpty) {
        try {
          final userMap = jsonDecode(userJson) as Map<String, dynamic>;
          return User.fromJson(userMap);
        } catch (e) {
          debugPrint('StorageService: Chyba pri parsovaní používateľa: $e');
          debugPrint('StorageService: Poškodené dáta: $userJson');
          // Vyčistíme poškodené dáta
          await removeUser();
          return null;
        }
      }
      return null;
    } catch (e) {
      debugPrint('StorageService: Chyba pri čítaní používateľa: $e');
      return null;
    }
  }

  static Future<void> removeUser() async {
    await _prefs?.remove(Constants.userKey);
  }

  // Clear all auth data
  static Future<void> clearAuthData() async {
    await removeToken();
    await removeUser();
  }

  // Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    try {
      if (_prefs == null) await init();
      final token = await getToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      debugPrint('StorageService: Chyba pri kontrole autentifikácie: $e');
      return false;
    }
  }
}
