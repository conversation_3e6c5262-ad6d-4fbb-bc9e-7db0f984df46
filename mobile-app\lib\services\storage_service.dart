import 'dart:convert';
// import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class StorageService {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
    } catch (e) {
      print('Error initializing SharedPreferences: $e');
      // Retry once
      try {
        _prefs = await SharedPreferences.getInstance();
      } catch (e2) {
        print('Failed to initialize SharedPreferences after retry: $e2');
      }
    }
  }

  // Token management (using shared preferences for web compatibility)
  static Future<void> setToken(String token) async {
    if (_prefs == null) await init();
    await _prefs?.setString(Constants.tokenKey, token);
  }

  static Future<String?> getToken() async {
    if (_prefs == null) await init();
    return _prefs?.getString(Constants.tokenKey);
  }

  static Future<void> removeToken() async {
    await _prefs?.remove(Constants.tokenKey);
  }

  // User management (shared preferences)
  static Future<void> setUser(User user) async {
    if (_prefs == null) await init();
    final userJson = jsonEncode(user.toJson());
    await _prefs?.setString(Constants.userKey, userJson);
  }

  static Future<User?> getUser() async {
    if (_prefs == null) await init();
    final userJson = _prefs?.getString(Constants.userKey);
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      } catch (e) {
        print('Error parsing user from storage: $e');
        return null;
      }
    }
    return null;
  }

  static Future<void> removeUser() async {
    await _prefs?.remove(Constants.userKey);
  }

  // Clear all auth data
  static Future<void> clearAuthData() async {
    await removeToken();
    await removeUser();
  }

  // Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    if (_prefs == null) await init();
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }
}
