import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class StorageService {
  static const _secureStorage = FlutterSecureStorage();
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Token management (secure storage)
  static Future<void> setToken(String token) async {
    await _secureStorage.write(key: Constants.tokenKey, value: token);
  }

  static Future<String?> getToken() async {
    return await _secureStorage.read(key: Constants.tokenKey);
  }

  static Future<void> removeToken() async {
    await _secureStorage.delete(key: Constants.tokenKey);
  }

  // User management (shared preferences)
  static Future<void> setUser(User user) async {
    final userJson = jsonEncode(user.toJson());
    await _prefs?.setString(Constants.userKey, userJson);
  }

  static Future<User?> getUser() async {
    final userJson = _prefs?.getString(Constants.userKey);
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      } catch (e) {
        print('Error parsing user from storage: $e');
        return null;
      }
    }
    return null;
  }

  static Future<void> removeUser() async {
    await _prefs?.remove(Constants.userKey);
  }

  // Clear all auth data
  static Future<void> clearAuthData() async {
    await removeToken();
    await removeUser();
  }

  // Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }
}
