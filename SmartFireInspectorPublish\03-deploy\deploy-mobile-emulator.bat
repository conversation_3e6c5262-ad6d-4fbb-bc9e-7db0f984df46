@echo off
echo ========================================
echo Smart Fire Inspector - Deploy to Emulator
echo ========================================
echo.

set SOURCE_DIR=c:\SmartFireInspector\mobile-app
set PUBLISH_DIR=c:\SmartFireInspectorPublish\published\mobile-app

echo Kontrolujem dostupne emulatory...
call C:\Android\android-sdk\emulator\emulator.exe -list-avds

echo.
echo Kontrolujem spustene emulatory...
call C:\Android\android-sdk\platform-tools\adb.exe devices

echo.
echo Vyberte sposob nasadenia:
echo 1. Nasadit do uz spusteneho emulatora
echo 2. Spustit Pixel 9 Pro emulator a nasadit
echo 3. Spustit Pixel Tablet emulator a nasadit
echo 4. Vybrat iny emulator
echo.
set /p emulator_choice="Zadajte volbu (1-4): "

if "%emulator_choice%"=="1" (
    echo Pouzivam uz spusteny emulator...
    set EMULATOR_TARGET=
) else if "%emulator_choice%"=="2" (
    echo Spustam Pixel 9 Pro emulator...
    start "" C:\Android\android-sdk\emulator\emulator.exe -avd Pixel_9_Pro
    set EMULATOR_TARGET=-s emulator-5554
    echo Cakam na spustenie emulatora...
    call :wait_for_emulator
) else if "%emulator_choice%"=="3" (
    echo Spustam Pixel Tablet emulator...
    start "" C:\Android\android-sdk\emulator\emulator.exe -avd Pixel_Tablet_API_34
    set EMULATOR_TARGET=-s emulator-5554
    echo Cakam na spustenie emulatora...
    call :wait_for_emulator
) else if "%emulator_choice%"=="4" (
    echo.
    echo Dostupne emulatory:
    call C:\Android\android-sdk\emulator\emulator.exe -list-avds
    echo.
    set /p avd_name="Zadajte nazov AVD: "
    echo Spustam emulator !avd_name!...
    start "" C:\Android\android-sdk\emulator\emulator.exe -avd !avd_name!
    set EMULATOR_TARGET=-s emulator-5554
    echo Cakam na spustenie emulatora...
    call :wait_for_emulator
) else (
    echo Neplatna volba!
    pause
    exit /b 1
)

echo.
echo Vyberte sposob buildu:
echo 1. Nasadit z uz vybudovaneho APK
echo 2. Build a nasadit debug verziu (rychle)
echo 3. Build a nasadit release verziu
echo.
set /p build_choice="Zadajte volbu (1-3): "

if "%build_choice%"=="1" (
    echo.
    echo Dostupne APK subory:
    if exist "%PUBLISH_DIR%\*.apk" (
        dir /b "%PUBLISH_DIR%\*.apk"
        echo.
        set /p apk_name="Zadajte nazov APK suboru: "
        set APK_PATH=%PUBLISH_DIR%\!apk_name!
    ) else (
        echo Ziadne APK subory nenajdene v %PUBLISH_DIR%
        echo Najprv spustite build-mobile.bat
        pause
        exit /b 1
    )
) else if "%build_choice%"=="2" (
    echo.
    echo Spustam debug build...
    cd /d "%SOURCE_DIR%"
    call C:\flutter\bin\flutter.bat build apk --debug
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Build zlyhal!
        pause
        exit /b 1
    )
    set APK_PATH=%SOURCE_DIR%\build\app\outputs\flutter-apk\app-debug.apk
) else if "%build_choice%"=="3" (
    echo.
    echo Spustam release build...
    cd /d "%SOURCE_DIR%"
    call C:\flutter\bin\flutter.bat build apk --release
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Build zlyhal!
        pause
        exit /b 1
    )
    set APK_PATH=%SOURCE_DIR%\build\app\outputs\flutter-apk\app-release.apk
) else (
    echo Neplatna volba!
    pause
    exit /b 1
)

echo.
echo Kontrolujem APK subor...
if not exist "%APK_PATH%" (
    echo CHYBA: APK subor neexistuje: %APK_PATH%
    pause
    exit /b 1
)

echo.
echo Odinstaluvavam predchadzajucu verziu...
call C:\Android\android-sdk\platform-tools\adb.exe %EMULATOR_TARGET% uninstall com.example.smart_fire_inspector

echo.
echo Instalujem novu verziu do emulatora...
echo APK: %APK_PATH%
call C:\Android\android-sdk\platform-tools\adb.exe %EMULATOR_TARGET% install "%APK_PATH%"

if %ERRORLEVEL% neq 0 (
    echo.
    echo CHYBA: Instalacia zlyhala!
    echo Skontrolujte, ci je emulator spusteny a pripojeny.
    pause
    exit /b 1
)

echo.
echo Spustam aplikaciu v emulatore...
call C:\Android\android-sdk\platform-tools\adb.exe %EMULATOR_TARGET% shell am start -n com.example.smart_fire_inspector/com.example.smart_fire_inspector.MainActivity

echo.
echo ========================================
echo USPECH: Aplikacia nasadena do emulatora!
echo ========================================
echo APK: %APK_PATH%
echo Emulator: %EMULATOR_TARGET%
echo Aplikacia by sa mala automaticky spustit v emulatore.
echo.
pause
exit /b 0

:wait_for_emulator
echo Cakam na spustenie emulatora (moze trvat 1-2 minuty)...
set /a counter=0
:wait_loop
timeout /t 5 /nobreak > nul
call C:\Android\android-sdk\platform-tools\adb.exe devices | find "emulator" > nul
if %ERRORLEVEL% equ 0 (
    echo Emulator je pripojeny!
    goto :eof
)
set /a counter+=1
if %counter% lss 24 (
    echo Stale cakam... (%counter%/24)
    goto :wait_loop
)
echo POZOR: Emulator sa nespustil v ocakavanom case. Pokracujem...
goto :eof
