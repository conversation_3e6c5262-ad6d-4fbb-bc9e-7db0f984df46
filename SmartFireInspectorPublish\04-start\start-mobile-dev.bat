@echo off
echo ========================================
echo Smart Fire Inspector - Start Mobile App (Development)
echo ========================================
echo.

set MOBILE_DIR=c:\SmartFireInspector\SmartFireInspectorPublish\mobile-app

echo Kontrolujem mobile app adresar...
if not exist "%MOBILE_DIR%" (
    echo CHYBA: Mobile app adresar neexistuje: %MOBILE_DIR%
    pause
    exit /b 1
)

echo Kontrolujem dostupne emulatory...
call C:\Android\android-sdk\emulator\emulator.exe -list-avds

echo.
echo Kontrolujem spustene emulatory...
call C:\Android\android-sdk\platform-tools\adb.exe devices

echo.
echo Vyberte sposob spustenia:
echo 1. Spustit v uz spustenom emulatore/zariadeni
echo 2. Spustit Pixel 9 Pro emulator a potom aplikaciu
echo 3. Spustit Pixel Tablet emulator a potom aplikaciu
echo 4. Len spustit aplikaciu (bez emulatora)
echo.
set /p choice="Zadajte volbu (1-4): "

if "%choice%"=="2" (
    echo Spustam Pixel 9 Pro emulator...
    start "Android Emulator - Pixel 9 Pro" C:\Android\android-sdk\emulator\emulator.exe -avd Pixel_9_Pro
    echo Cakam na spustenie emulatora...
    timeout /t 20 /nobreak > nul
) else if "%choice%"=="3" (
    echo Spustam Pixel Tablet emulator...
    start "Android Emulator - Pixel Tablet" C:\Android\android-sdk\emulator\emulator.exe -avd Pixel_Tablet_API_34
    echo Cakam na spustenie emulatora...
    timeout /t 20 /nobreak > nul
)

echo.
echo Spustam Flutter aplikaciu v development mode...
echo.
echo Aplikacia sa spusti s hot reload podporou.
echo Pre hot reload stlacte 'r' v terminali.
echo Pre restart stlacte 'R' v terminali.
echo Pre ukoncenie stlacte 'q' v terminali alebo Ctrl+C.
echo.

cd /d "%MOBILE_DIR%"

echo Kontrolujem dependencies...
call C:\flutter\bin\flutter.bat pub get

echo Spustam aplikaciu...
call C:\flutter\bin\flutter.bat run

pause
