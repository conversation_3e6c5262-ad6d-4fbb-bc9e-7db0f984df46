========================================
Smart Fire Inspector - Publish Scripts
========================================

Tento adresar obsahuje organizovane skripty pre publikovanie a nasadenie vsetkych komponentov Smart Fire Inspector aplikacie.

STRUKTURA ADRESAROV:
====================

01-config/                 - Konfiguracne skripty
├── mobile-api-config.bat     - Konfiguracia API endpointu pre mobilnu app
└── environment-setup.bat     - Kontrola vyvojarskeho prostredia

02-publish/                - Publikovacie skripty
├── publish-all.bat           - Publikuje vsetky komponenty naraz
├── publish-backend.bat       - Publikuje Backend API (.NET)
├── publish-webapp.bat        - Publikuje Web Application (React)
└── build-mobile.bat          - Builduje Mobile Application (Flutter)

03-deploy/                 - Nasadzovacie skripty
├── deploy-mobile-usb.bat     - Nasadenie na telefon cez USB
└── deploy-mobile-emulator.bat - Nasadenie do Android emulatora

04-start/                  - Spustacie skripty pre development
├── start-all-services.bat    - Spusti vsetky sluzby naraz
├── start-backend-dev.bat     - Spusti len Backend API
├── start-webapp-dev.bat      - Spusti len Web Application
└── start-mobile-dev.bat      - Spusti len Mobile Application

published/                 - Publikovane verzie (vytvorene automaticky)
├── backend-api/              - Publikovany .NET API
├── web-app/                  - Publikovana React aplikacia
└── mobile-app/               - Flutter APK/AAB subory

RYCHLY START:
=============

1. KOMPLETNE PUBLIKOVANIE:
   - Spustite: publish-all.bat
   - Vyberte volbu 1 (Vsetko)
   - Postupujte podla instrukcii

2. LEN BACKEND API:
   - Spustite: publish-backend.bat
   - Spustite: backend-api\start-api.bat

3. LEN WEB APLIKACIA:
   - Spustite: publish-webapp.bat
   - Spustite: web-app\start-dev-server.bat

4. MOBILNA APLIKACIA:
   a) Konfiguracia:
      - Spustite: mobile-config.bat
      - Vyberte typ nasadenia (emulator/telefon/produkcia)
   
   b) Build:
      - Spustite: build-mobile.bat
      - Vyberte typ buildu (debug/release/bundle)
   
   c) Nasadenie:
      - Pre telefon: deploy-mobile-usb.bat
      - Pre emulator: deploy-mobile-emulator.bat

KONFIGURACIA API ENDPOINTU:
===========================

Pre mobilnu aplikaciu je potrebne nakonfigurovat spravny API endpoint:

1. LOKALNY DEVELOPMENT (EMULATOR):
   - URL: http://********:5000
   - Pouzite ked testujete v Android emulatore
   - API server musi bezat na vasej lokalnej masine

2. LOKALNY DEVELOPMENT (FYZICKY TELEFON):
   - URL: http://[VASA_IP]:5000
   - Napriklad: http://*************:5000
   - Telefon a pocitac musia byt v rovnakej sieti
   - Zistite IP adresu: ipconfig (Windows) alebo ifconfig (Linux/Mac)

3. PRODUKCIA:
   - URL: https://vasa-domena.com/api
   - Pre nasadenie na web server
   - Potrebujete HTTPS pre produkciu

POZIADAVKY:
===========

BACKEND API:
- .NET 8 SDK
- PostgreSQL databaza (Supabase)

WEB APPLICATION:
- Node.js 18+
- npm alebo yarn

MOBILE APPLICATION:
- Flutter SDK
- Android SDK
- Android Studio (pre emulatory)

STRUKTURA PUBLISH ADRESAROV:
============================

Po spusteni skriptov sa vytvoria:

c:\SmartFireInspectorPublish\
├── backend-api\              - Publikovany .NET API
│   ├── SmartFireInspector.Api.exe
│   ├── start-api.bat
│   └── README.txt
├── web-app\                  - Publikovana React aplikacia
│   ├── index.html
│   ├── assets\
│   ├── start-dev-server.bat
│   └── README.txt
└── mobile-app\               - Flutter APK/AAB subory
    ├── smart-fire-inspector-debug.apk
    ├── smart-fire-inspector-release.apk
    └── README.txt

TROUBLESHOOTING:
================

1. "dotnet nie je rozpoznany ako prikaz"
   - Nainstalujte .NET 8 SDK
   - Restartujte command prompt

2. "npm nie je rozpoznany ako prikaz"
   - Nainstalujte Node.js
   - Restartujte command prompt

3. "flutter nie je rozpoznany ako prikaz"
   - Nainstalujte Flutter SDK
   - Pridajte Flutter do PATH

4. "adb nie je rozpoznany ako prikaz"
   - Nainstalujte Android SDK
   - Pridajte platform-tools do PATH

5. Mobile app sa nepripoji k API:
   - Skontrolujte API endpoint v mobile-config.bat
   - Overte ze API server bezi
   - Pre telefon: skontrolujte IP adresu a firewall

KONTAKT:
========

Pre podporu a otazky kontaktujte vyvojovy tim.

Posledna aktualizacia: 2025-01-05
