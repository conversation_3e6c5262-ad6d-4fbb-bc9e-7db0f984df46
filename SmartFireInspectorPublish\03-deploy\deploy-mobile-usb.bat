@echo off
echo ========================================
echo Smart Fire Inspector - Deploy to USB Device
echo ========================================
echo.

set SOURCE_DIR=c:\SmartFireInspector\mobile-app
set PUBLISH_DIR=c:\SmartFireInspector\SmartFireInspectorPublish\published\mobile-app

echo Kontrolujem pripojene zariadenia...
call C:\Android\android-sdk\platform-tools\adb.exe devices

echo.
echo POZOR: Pred pokracovanim sa uistite, ze:
echo 1. Mate pripojene Android zariadenie cez USB
echo 2. Mate povolene Developer Options
echo 3. Mate povoleny USB Debugging
echo 4. Mate povolenu instalaciu z neznamy zdrojov
echo.
set /p continue="Pokracovat? (y/n): "
if /i not "%continue%"=="y" exit /b 0

echo.
echo Vyberte sposob nasadenia:
echo 1. Nasadit z uz vybudovaneho APK
echo 2. Build a nasadit debug verziu (rychle)
echo 3. Build a nasadit release verziu
echo.
set /p deploy_choice="Zadajte volbu (1-3): "

if "%deploy_choice%"=="1" (
    echo.
    echo Dostupne APK subory:
    if exist "%PUBLISH_DIR%\*.apk" (
        dir /b "%PUBLISH_DIR%\*.apk"
        echo.
        set /p apk_name="Zadajte nazov APK suboru: "
        set APK_PATH=%PUBLISH_DIR%\!apk_name!
    ) else (
        echo Ziadne APK subory nenajdene v %PUBLISH_DIR%
        echo Najprv spustite build-mobile.bat
        pause
        exit /b 1
    )
) else if "%deploy_choice%"=="2" (
    echo.
    echo Spustam debug build...
    cd /d "%SOURCE_DIR%"
    call C:\flutter\bin\flutter.bat build apk --debug
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Build zlyhal!
        pause
        exit /b 1
    )
    set APK_PATH=%SOURCE_DIR%\build\app\outputs\flutter-apk\app-debug.apk
) else if "%deploy_choice%"=="3" (
    echo.
    echo Spustam release build...
    cd /d "%SOURCE_DIR%"
    call C:\flutter\bin\flutter.bat build apk --release
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Build zlyhal!
        pause
        exit /b 1
    )
    set APK_PATH=%SOURCE_DIR%\build\app\outputs\flutter-apk\app-release.apk
) else (
    echo Neplatna volba!
    pause
    exit /b 1
)

echo.
echo Kontrolujem APK subor...
if not exist "%APK_PATH%" (
    echo CHYBA: APK subor neexistuje: %APK_PATH%
    pause
    exit /b 1
)

echo.
echo Kontrolujem pripojene zariadenie...
for /f "tokens=1" %%i in ('C:\Android\android-sdk\platform-tools\adb.exe devices ^| find /c "device"') do set DEVICE_COUNT=%%i
if %DEVICE_COUNT% lss 1 (
    echo CHYBA: Ziadne zariadenie nie je pripojene!
    echo Skontrolujte USB pripojenie a USB Debugging.
    pause
    exit /b 1
)

echo.
echo Odinstaluvavam predchadzajucu verziu (ak existuje)...
call C:\Android\android-sdk\platform-tools\adb.exe uninstall com.example.smart_fire_inspector

echo.
echo Instalujem novu verziu...
echo APK: %APK_PATH%
call C:\Android\android-sdk\platform-tools\adb.exe install "%APK_PATH%"

if %ERRORLEVEL% neq 0 (
    echo.
    echo CHYBA: Instalacia zlyhala!
    echo.
    echo Mozne priciny:
    echo - Zariadenie nie je pripojene
    echo - USB Debugging nie je povoleny
    echo - Instalacia z neznamy zdrojov nie je povolena
    echo - APK subor je poskodeny
    echo - Nedostatok miesta na zariadeni
    echo.
    pause
    exit /b 1
)

echo.
echo Spustam aplikaciu...
call C:\Android\android-sdk\platform-tools\adb.exe shell am start -n com.example.smart_fire_inspector/com.example.smart_fire_inspector.MainActivity

echo.
echo Kontrolujem ci sa aplikacia spustila...
timeout /t 3 /nobreak > nul
call C:\Android\android-sdk\platform-tools\adb.exe shell "ps | grep smart_fire_inspector" > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo Aplikacia sa uspesne spustila!
) else (
    echo Aplikacia sa mozno nespustila automaticky. Skuste ju spustit manualne.
)

echo.
echo ========================================
echo USPECH: Aplikacia nasadena na zariadenie!
echo ========================================
echo APK: %APK_PATH%
echo Zariadenie: USB pripojene
echo.
echo Aplikacia by sa mala automaticky spustit na zariadeni.
echo Ak nie, najdite ju v menu aplikacii.
echo.
pause
