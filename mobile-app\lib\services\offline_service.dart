import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';

class OfflineService {
  static const String _offlineUsersKey = 'offline_users';
  static const String _lastSyncKey = 'last_sync';
  static const String _offlineDataKey = 'offline_data';

  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Offline user authentication
  static Future<void> saveOfflineUser(User user, String password) async {
    final offlineUsers = await getOfflineUsers();
    
    // Simple password hashing (in production use proper hashing)
    final hashedPassword = _simpleHash(password);
    
    offlineUsers[user.email] = {
      'user': user.toJson(),
      'password': hashedPassword,
      'lastLogin': DateTime.now().toIso8601String(),
    };

    await _prefs?.setString(_offlineUsersKey, jsonEncode(offlineUsers));
    print('💾 User saved for offline access: ${user.email}');
  }

  static Future<Map<String, dynamic>> getOfflineUsers() async {
    final data = _prefs?.getString(_offlineUsersKey);
    if (data != null) {
      try {
        return Map<String, dynamic>.from(jsonDecode(data));
      } catch (e) {
        print('Error parsing offline users: $e');
      }
    }
    return {};
  }

  static Future<AuthResponse> authenticateOffline(String email, String password) async {
    final offlineUsers = await getOfflineUsers();
    final userData = offlineUsers[email.toLowerCase()];

    if (userData == null) {
      return AuthResponse(
        success: false,
        message: 'Používateľ nie je dostupný offline. Prihlás sa online najprv.',
      );
    }

    final hashedPassword = _simpleHash(password);
    if (userData['password'] != hashedPassword) {
      return AuthResponse(
        success: false,
        message: 'Nesprávne heslo',
      );
    }

    try {
      final user = User.fromJson(userData['user']);
      
      // Update last login
      userData['lastLogin'] = DateTime.now().toIso8601String();
      offlineUsers[email.toLowerCase()] = userData;
      await _prefs?.setString(_offlineUsersKey, jsonEncode(offlineUsers));

      return AuthResponse(
        success: true,
        message: 'Offline prihlásenie úspešné',
        token: 'offline_token_${user.id}',
        user: user,
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'Chyba pri offline prihlásení: $e',
      );
    }
  }

  static Future<List<String>> getOfflineUserEmails() async {
    final offlineUsers = await getOfflineUsers();
    return offlineUsers.keys.toList();
  }

  static Future<void> clearOfflineUsers() async {
    await _prefs?.remove(_offlineUsersKey);
    print('🗑️ Offline users cleared');
  }

  // Sync management
  static Future<void> setLastSyncTime() async {
    await _prefs?.setString(_lastSyncKey, DateTime.now().toIso8601String());
  }

  static Future<DateTime?> getLastSyncTime() async {
    final syncTime = _prefs?.getString(_lastSyncKey);
    if (syncTime != null) {
      try {
        return DateTime.parse(syncTime);
      } catch (e) {
        print('Error parsing last sync time: $e');
      }
    }
    return null;
  }

  // Offline data storage (for future use with inspections, devices, etc.)
  static Future<void> saveOfflineData(String key, Map<String, dynamic> data) async {
    final offlineData = await _getOfflineData();
    offlineData[key] = {
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
      'synced': false,
    };
    
    await _prefs?.setString(_offlineDataKey, jsonEncode(offlineData));
    print('💾 Offline data saved: $key');
  }

  static Future<Map<String, dynamic>?> getOfflineData(String key) async {
    final offlineData = await _getOfflineData();
    final item = offlineData[key];
    return item?['data'];
  }

  static Future<List<String>> getUnsyncedDataKeys() async {
    final offlineData = await _getOfflineData();
    return offlineData.entries
        .where((entry) => entry.value['synced'] == false)
        .map((entry) => entry.key)
        .toList();
  }

  static Future<void> markDataAsSynced(String key) async {
    final offlineData = await _getOfflineData();
    if (offlineData.containsKey(key)) {
      offlineData[key]['synced'] = true;
      await _prefs?.setString(_offlineDataKey, jsonEncode(offlineData));
    }
  }

  static Future<Map<String, dynamic>> _getOfflineData() async {
    final data = _prefs?.getString(_offlineDataKey);
    if (data != null) {
      try {
        return Map<String, dynamic>.from(jsonDecode(data));
      } catch (e) {
        print('Error parsing offline data: $e');
      }
    }
    return {};
  }

  // Simple password hashing (use proper hashing in production)
  static String _simpleHash(String password) {
    // This is just for demo - use bcrypt or similar in production
    return password.split('').map((c) => c.codeUnitAt(0)).join('');
  }

  // Check if user has offline access
  static Future<bool> hasOfflineAccess(String email) async {
    final offlineUsers = await getOfflineUsers();
    return offlineUsers.containsKey(email.toLowerCase());
  }

  // Get offline user info
  static Future<User?> getOfflineUser(String email) async {
    final offlineUsers = await getOfflineUsers();
    final userData = offlineUsers[email.toLowerCase()];
    
    if (userData != null) {
      try {
        return User.fromJson(userData['user']);
      } catch (e) {
        print('Error parsing offline user: $e');
      }
    }
    return null;
  }
}
