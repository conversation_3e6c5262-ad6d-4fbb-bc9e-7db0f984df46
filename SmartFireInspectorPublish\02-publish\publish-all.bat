@echo off
echo ========================================
echo Smart Fire Inspector - Publish All Projects
echo ========================================
echo.

echo Tento skript publikuje vsetky komponenty Smart Fire Inspector:
echo 1. Backend API (.NET)
echo 2. Web Application (React)
echo 3. Mobile Application (Flutter)
echo.

echo Vyberte co chcete publikovat:
echo 1. Vsetko (Backend + Web + Mobile)
echo 2. Len Backend API
echo 3. Len Web Application
echo 4. Len Mobile Application
echo 5. Backend + Web (bez Mobile)
echo 6. Backend + Mobile (bez Web)
echo 7. Web + Mobile (bez Backend)
echo.
set /p choice="Zadajte volbu (1-7): "

set PUBLISH_BACKEND=0
set PUBLISH_WEB=0
set PUBLISH_MOBILE=0

if "%choice%"=="1" (
    set PUBLISH_BACKEND=1
    set PUBLISH_WEB=1
    set PUBLISH_MOBILE=1
) else if "%choice%"=="2" (
    set PUBLISH_BACKEND=1
) else if "%choice%"=="3" (
    set PUBLISH_WEB=1
) else if "%choice%"=="4" (
    set PUBLISH_MOBILE=1
) else if "%choice%"=="5" (
    set PUBLISH_BACKEND=1
    set PUBLISH_WEB=1
) else if "%choice%"=="6" (
    set PUBLISH_BACKEND=1
    set PUBLISH_MOBILE=1
) else if "%choice%"=="7" (
    set PUBLISH_WEB=1
    set PUBLISH_MOBILE=1
) else (
    echo Neplatna volba!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Zacinam publikovanie...
echo ========================================

set ERROR_COUNT=0

if "%PUBLISH_BACKEND%"=="1" (
    echo.
    echo [1/3] Publikujem Backend API...
    call "%~dp0publish-backend.bat"
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Backend publish zlyhal!
        set /a ERROR_COUNT+=1
    ) else (
        echo Backend API publikovany uspesne!
    )
)

if "%PUBLISH_WEB%"=="1" (
    echo.
    echo [2/3] Publikujem Web Application...
    call "%~dp0publish-webapp.bat"
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Web App publish zlyhal!
        set /a ERROR_COUNT+=1
    ) else (
        echo Web Application publikovana uspesne!
    )
)

if "%PUBLISH_MOBILE%"=="1" (
    echo.
    echo [3/3] Konfiguracia Mobile Application...
    echo Najprv nakonfigurujte API endpoint pre mobilnu aplikaciu:
    call "..\01-config\mobile-api-config.bat"
    
    echo.
    echo Teraz publikujem Mobile Application...
    call "%~dp0build-mobile.bat"
    if %ERRORLEVEL% neq 0 (
        echo CHYBA: Mobile App build zlyhal!
        set /a ERROR_COUNT+=1
    ) else (
        echo Mobile Application publikovana uspesne!
    )
)

echo.
echo ========================================
if %ERROR_COUNT% equ 0 (
    echo USPECH: Publikovanie dokoncene bez chyb!
) else (
    echo POZOR: Publikovanie dokoncene s %ERROR_COUNT% chybami!
)
echo ========================================
echo.

echo Publikovane komponenty:
if "%PUBLISH_BACKEND%"=="1" (
    echo - Backend API: c:\SmartFireInspectorPublish\published\backend-api\
)
if "%PUBLISH_WEB%"=="1" (
    echo - Web App: c:\SmartFireInspectorPublish\published\web-app\
)
if "%PUBLISH_MOBILE%"=="1" (
    echo - Mobile App: c:\SmartFireInspectorPublish\published\mobile-app\
)

echo.
echo Dalsi kroky:
echo - Pre spustenie: Pouzite skripty v 04-start\
echo - Pre nasadenie mobile app: Pouzite skripty v 03-deploy\
echo.
pause
