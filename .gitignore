# .NET Core
backend/bin/
backend/obj/
backend/out/
backend/.vs/
backend/*.user
backend/*.suo
backend/*.cache
backend/appsettings.Development.json

# React
web-app/node_modules/
web-app/dist/
web-app/.env.local
web-app/.env.development.local
web-app/.env.test.local
web-app/.env.production.local
web-app/npm-debug.log*
web-app/yarn-debug.log*
web-app/yarn-error.log*

# Flutter
mobile-app/.dart_tool/
mobile-app/.flutter-plugins
mobile-app/.flutter-plugins-dependencies
mobile-app/.packages
mobile-app/.pub-cache/
mobile-app/.pub/
mobile-app/build/
mobile-app/ios/Pods/
mobile-app/ios/.symlinks/
mobile-app/ios/Flutter/Flutter.framework
mobile-app/ios/Flutter/Flutter.podspec

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Environment variables
.env
.env.local
.env.development
.env.production

# Logs
*.log
logs/

# Database
*.db
*.sqlite
