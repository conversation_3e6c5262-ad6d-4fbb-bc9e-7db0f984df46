@echo off
echo ========================================
echo Smart Fire Inspector - Mobile App Config
echo ========================================
echo.

set SOURCE_DIR=c:\SmartFireInspector\mobile-app
set CONFIG_FILE=%SOURCE_DIR%\lib\utils\api_config.dart

echo Aktualna konfiguracia API endpointu:
echo.

if exist "%CONFIG_FILE%" (
    echo Existujuca konfiguracia:
    type "%CONFIG_FILE%"
    echo.
) else (
    echo Konfiguracia neexistuje, vytvorim novu...
)

echo.
echo Vyberte typ nasadenia:
echo 1. Lokalny development (emulator) - http://********:5000
echo 2. Lokalny development (fyzicky telefon) - http://192.168.1.X:5000
echo 3. Produkcia (web server) - https://your-domain.com/api
echo 4. Custom IP adresa
echo.
set /p choice="Zadajte volbu (1-4): "

if "%choice%"=="1" (
    set API_URL=http://********:5000
    set DESCRIPTION=Lokalny development - emulator
) else if "%choice%"=="2" (
    echo.
    echo Zadajte IP adresu vasho pocitaca v lokalnej sieti:
    echo Napriklad: *************, ************, atd.
    set /p local_ip="IP adresa: "
    set API_URL=http://!local_ip!:5000
    set DESCRIPTION=Lokalny development - fyzicky telefon
) else if "%choice%"=="3" (
    echo.
    set /p prod_url="Zadajte URL produkcie API (napr. https://api.smartfire.sk): "
    set API_URL=!prod_url!
    set DESCRIPTION=Produkcia
) else if "%choice%"=="4" (
    echo.
    set /p custom_url="Zadajte custom URL (napr. http://*************:8080): "
    set API_URL=!custom_url!
    set DESCRIPTION=Custom konfiguracia
) else (
    echo Neplatna volba!
    pause
    exit /b 1
)

echo.
echo Vytvariam konfiguracny subor...

echo class ApiConfig { > "%CONFIG_FILE%"
echo   // %DESCRIPTION% >> "%CONFIG_FILE%"
echo   static const String baseUrl = '%API_URL%'; >> "%CONFIG_FILE%"
echo. >> "%CONFIG_FILE%"
echo   // API endpoints >> "%CONFIG_FILE%"
echo   static const String authEndpoint = '/api/auth'; >> "%CONFIG_FILE%"
echo   static const String devicesEndpoint = '/api/devices'; >> "%CONFIG_FILE%"
echo   static const String inspectionsEndpoint = '/api/inspections'; >> "%CONFIG_FILE%"
echo   static const String reportsEndpoint = '/api/reports'; >> "%CONFIG_FILE%"
echo. >> "%CONFIG_FILE%"
echo   // Timeout nastavenia >> "%CONFIG_FILE%"
echo   static const Duration requestTimeout = Duration(seconds: 30); >> "%CONFIG_FILE%"
echo   static const Duration connectionTimeout = Duration(seconds: 10); >> "%CONFIG_FILE%"
echo. >> "%CONFIG_FILE%"
echo   // Debug nastavenia >> "%CONFIG_FILE%"
echo   static const bool enableLogging = true; >> "%CONFIG_FILE%"
echo } >> "%CONFIG_FILE%"

echo.
echo ========================================
echo USPECH: Konfiguracia vytvorena!
echo ========================================
echo API URL: %API_URL%
echo Typ: %DESCRIPTION%
echo Subor: %CONFIG_FILE%
echo.
echo Teraz mozete pouzit build skripty pre mobilnu aplikaciu.
echo.
pause
