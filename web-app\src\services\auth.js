import api from './api';
import storageService from './storage';
import { API_ENDPOINTS } from '../utils/constants';

class AuthService {
  async login(email, password) {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.LOGIN, {
        email,
        password
      });

      const { success, token, user, message } = response.data;

      if (success && token && user) {
        storageService.setToken(token);
        storageService.setUser(user);
        return { success: true, user, message };
      } else {
        return { success: false, message: message || 'Prihlásenie zlyhalo' };
      }
    } catch (error) {
      console.error('Login error:', error);
      
      if (error.response?.data?.message) {
        return { success: false, message: error.response.data.message };
      }
      
      return { 
        success: false, 
        message: 'Chyba pri komunikácii so serverom' 
      };
    }
  }

  logout() {
    storageService.clearAuthData();
  }

  getCurrentUser() {
    return storageService.getUser();
  }

  isAuthenticated() {
    return storageService.isAuthenticated();
  }

  async getTestAccounts() {
    try {
      const response = await api.get(API_ENDPOINTS.AUTH.TEST_ACCOUNTS);
      return response.data;
    } catch (error) {
      console.error('Error fetching test accounts:', error);
      return null;
    }
  }
}

export default new AuthService();
