-- Vytvorenie tabuľky obrázkov kontrol
CREATE TABLE IF NOT EXISTS inspection_images (
    id SERIAL PRIMARY KEY,
    inspection_id INTEGER NOT NULL REFERENCES inspections(id) ON DELETE CASCADE,
    file_name VA<PERSON>HAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    storage_url VARCHAR(500) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexy pre výkon
CREATE INDEX IF NOT EXISTS idx_inspection_images_inspection_id ON inspection_images(inspection_id);
CREATE INDEX IF NOT EXISTS idx_inspection_images_file_name ON inspection_images(file_name);
CREATE INDEX IF NOT EXISTS idx_inspection_images_created_at ON inspection_images(created_at);
