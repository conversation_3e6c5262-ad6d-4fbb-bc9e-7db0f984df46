========================================
Smart Fire Inspector - Backend Fix
========================================

PROBLEM:
========

start-backend-dev.bat koncil s chybou:
"Could not copy apphost.exe to SmartFireInspector.Api.exe. 
The file is locked by: SmartFireInspector.Api (5768)"

PRICINA:
========

Backend API uz bezal a subor SmartFireInspector.Api.exe bol zamknuty.
.NET nemohol prepist executable subor pocas build procesu.

RIESENIE:
=========

1. AUTOMATICKE UKONCENIE PROCESOV:
   - Skript najprv hlada beziacich SmartFireInspector.Api.exe procesov
   - Ukoncuje ich pomocou taskkill /F /IM SmartFireInspector.Api.exe /T
   - Kontroluje a uvolnuje porty 5000 a 5001

2. CISTENIE BUILD CACHE:
   - Spusta dotnet clean
   - Maze bin a obj adresare
   - Odstranuje zamknute subory

3. POSTUPNY BUILD A RUN:
   - Najprv dotnet build (s error handlingom)
   - Ak build zlyha, skusi dotnet restore a build znovu
   - Potom dotnet run

4. DETAILNE ERROR HANDLING:
   - Jasne chybove spravy
   - Navody na riesenie problemov
   - Odkazy na pomocne skripty

NOVA FUNKCIONALITA:
==================

1. CLEANUP-BACKEND.BAT:
   - Ukoncuje vsetky .NET procesy
   - Uvolnuje porty 5000 a 5001
   - Maze build cache
   - Cistenie NuGet cache

2. TEST-BACKEND-FIX.BAT:
   - Testuje vsetky komponenty bez spustenia
   - Kontroluje .NET SDK
   - Overuje projekt subory
   - Testuje dotnet restore a build

3. VYLEPŠENY START-BACKEND-DEV.BAT:
   - 3-fazovy proces: cleanup -> build -> run
   - Automaticke ukoncenie beziacich procesov
   - Postupny build s error handlingom
   - Detailne chybove spravy

POUZITIE:
=========

NORMALNY WORKFLOW:
1. start-backend-dev.bat (automaticky vyriesí vsetko)

AK SA VYSKYTNU PROBLEMY:
1. cleanup-backend.bat (vycisti vsetko)
2. test-backend-fix.bat (otestuje komponenty)
3. start-backend-dev.bat (spusti backend)

MANUALNE RIESENIE:
1. taskkill /F /IM SmartFireInspector.Api.exe /T
2. cd c:\SmartFireInspector\backend
3. dotnet clean
4. rmdir /s /q bin obj
5. dotnet restore
6. dotnet build
7. dotnet run --urls=http://0.0.0.0:5000

TECHNICKÉ DETAILY:
==================

1. PROCESS CLEANUP:
   - tasklist /FI "IMAGENAME eq SmartFireInspector.Api.exe"
   - taskkill /F /IM SmartFireInspector.Api.exe /T
   - timeout /t 2 /nobreak (cakanie na ukoncenie)

2. PORT CLEANUP:
   - netstat -ano | findstr :5000
   - taskkill /F /PID <pid> (ukoncenie procesu na porte)

3. BUILD CACHE CLEANUP:
   - dotnet clean
   - rmdir /s /q bin obj
   - dotnet nuget locals all --clear

4. ERROR DETECTION:
   - if %ERRORLEVEL% neq 0 (detekcia chyby)
   - goto :error_exit (skok na error handling)

CHYBOVE SPRAVY:
===============

CHYBA: "Backend adresar neexistuje"
RIESENIE: Skontrolujte cestu c:\SmartFireInspector\backend

CHYBA: "dotnet nie je rozpoznany"
RIESENIE: Nainstalujte .NET 8 SDK

CHYBA: "Build zlyhal"
RIESENIE: 
1. Skontrolujte kod v Visual Studio
2. Spustite dotnet restore
3. Opravte chyby v kode

CHYBA: "Port 5000 je obsadeny"
RIESENIE:
1. Spustite cleanup-backend.bat
2. Alebo restartujte pocitac

CHYBA: "File is locked"
RIESENIE:
1. Spustite cleanup-backend.bat
2. Ukoncite vsetky Visual Studio instance
3. Restartujte pocitac

PREVENTIVNE OPATRENIA:
=====================

1. PRED SPUSTENIM:
   - Zatvorte Visual Studio
   - Ukoncite vsetky dotnet procesy
   - Skontrolujte, ci port 5000 je volny

2. PO UKONCENI:
   - Pouzite Ctrl+C na ukoncenie
   - Nenasilne zatvarajte okno
   - Nechajte proces korektne ukoncit

3. PRI PROBLEMOCH:
   - Spustite cleanup-backend.bat
   - Restartujte pocitac
   - Reinstalujte .NET SDK

KOMPATIBILITA:
==============

- Windows 10/11
- .NET 8 SDK
- PowerShell/Command Prompt
- Visual Studio 2022 (optional)

POSLEDNA AKTUALIZACIA: 2025-01-05
