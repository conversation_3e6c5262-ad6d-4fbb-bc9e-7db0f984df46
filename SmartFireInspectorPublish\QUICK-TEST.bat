@echo off
echo ========================================
echo Smart Fire Inspector - Quick Test
echo ========================================
echo.

echo Testujem master menu funkcionalnost...
echo.

echo Aktualna cesta: %CD%
echo.

echo Kontrolujem existenciu suborov:
echo.

if exist "MASTER-MENU.bat" (
    echo [OK] MASTER-MENU.bat existuje
) else (
    echo [CHYBA] MASTER-MENU.bat neexistuje
)

if exist "01-config\environment-setup.bat" (
    echo [OK] 01-config\environment-setup.bat existuje
) else (
    echo [CHYBA] 01-config\environment-setup.bat neexistuje
)

if exist "01-config\mobile-api-config.bat" (
    echo [OK] 01-config\mobile-api-config.bat existuje
) else (
    echo [CHYBA] 01-config\mobile-api-config.bat neexistuje
)

if exist "04-start\start-all-services.bat" (
    echo [OK] 04-start\start-all-services.bat existuje
) else (
    echo [CHYBA] 04-start\start-all-services.bat neexistuje
)

echo.
echo Testujem spustenie jednoducheho skriptu...
echo.

echo Vytvariam testovaci skript...
echo @echo off > test-simple.bat
echo echo Testovaci skript funguje! >> test-simple.bat
echo echo Datum: %%date%% %%time%% >> test-simple.bat
echo pause >> test-simple.bat

echo Spustam testovaci skript...
call test-simple.bat

echo.
echo Mazem testovaci skript...
del test-simple.bat

echo.
echo ========================================
echo QUICK TEST DOKONCENY!
echo ========================================
echo.
echo Ak vsetko funguje, mozete spustit MASTER-MENU.bat
echo.
echo Ak sa master menu stale zaviera, skuste:
echo 1. Spustit ako administrator
echo 2. Skontrolovat, ci ste v spravnom adresari
echo 3. Pouzit jednotlive skripty priamo
echo.
pause
