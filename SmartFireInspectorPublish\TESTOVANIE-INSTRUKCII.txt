========================================
Smart Fire Inspector - Testovanie Instrukcii
========================================

PROBLEM S KONZOLOU SA ZAVIERA:
==============================

Ak sa vam po zadani volby zaviera konzolove okno, skuste tieto riesenia:

1. SPUSTENIE AKO ADMINISTRATOR:
   - Kliknite pravym na START-HERE.bat
   - Vyberte "Spustit ako administrator"

2. SPUSTENIE Z COMMAND PROMPT:
   - Otvorte Command Prompt (cmd)
   - Prejdite do adresara: cd c:\SmartFireInspector\SmartFireInspectorPublish
   - Spustite: START-HERE.bat

3. SPUSTENIE CEZ WINDOWS EXPLORER:
   - Otvorte Windows Explorer
   - Prejdite do c:\SmartFireInspector\SmartFireInspectorPublish\
   - Dvojklik na START-HERE.bat

4. ALTERNATIVNE MENU:
   - Pouzite MASTER-MENU-FIXED.bat namiesto MASTER-MENU.bat
   - Tento ma lepsi error handling

TESTOVANIE POSTUPNOSTI:
======================

1. ZAKLADNY TEST:
   START-HERE.bat
   -> Vyberte volbu 2 (rychly test)
   -> Skontrolujte, ci sa zobrazia OK spravy

2. MASTER MENU TEST:
   START-HERE.bat
   -> Vyberte volbu 1 (Master Menu)
   -> Vyberte volbu 6 (Test funkcnosti)
   -> Skontrolujte, ci sa zobrazia adresare

3. KONFIGURACNY TEST:
   Master Menu -> volba 1 (Kontrola prostredia)
   -> Skontroluje .NET, Node.js, Flutter, Android SDK

4. MOBILE CONFIG TEST:
   Master Menu -> volba 2 (Konfiguracia mobile API)
   -> Nakonfiguruje API endpoint pre mobilnu app

5. START SERVICES TEST:
   Master Menu -> volba 4 (Spustit vsetky sluzby)
   -> Spusti Backend, Web App, Mobile App

RIESENIE PROBLEMOV:
==================

PROBLEM: "Subor neexistuje"
RIESENIE: Skontrolujte, ci ste v spravnom adresari c:\SmartFireInspector\SmartFireInspectorPublish\

PROBLEM: "dotnet nie je rozpoznany"
RIESENIE: Nainstalujte .NET 8 SDK

PROBLEM: "npm nie je rozpoznany"
RIESENIE: Nainstalujte Node.js

PROBLEM: "flutter nie je rozpoznany"
RIESENIE: Nainstalujte Flutter SDK do C:\flutter\

PROBLEM: Konzola sa zaviera okamzite
RIESENIE: 
- Spustite ako administrator
- Pouzite Command Prompt
- Skontrolujte antivirus (moze blokovat BAT subory)

STRUKTURA SUBOROV:
=================

c:\SmartFireInspector\SmartFireInspectorPublish\
├── START-HERE.bat              # ZACNITE TU!
├── MASTER-MENU-FIXED.bat       # Hlavne menu (opravena verzia)
├── TESTOVANIE-INSTRUKCII.txt   # Tento subor
├── 01-config\
│   ├── environment-setup.bat
│   └── mobile-api-config.bat
├── 02-publish\
│   ├── publish-all.bat
│   ├── publish-backend.bat
│   ├── publish-webapp.bat
│   └── build-mobile.bat
├── 03-deploy\
│   ├── deploy-mobile-usb.bat
│   └── deploy-mobile-emulator.bat
└── 04-start\
    ├── start-all-services.bat
    ├── start-backend-dev.bat
    ├── start-webapp-dev.bat
    └── start-mobile-dev.bat

RYCHLE SPUSTENIE:
================

1. Otvorte Command Prompt
2. cd c:\SmartFireInspector\SmartFireInspectorPublish
3. START-HERE.bat
4. Vyberte volbu 1
5. Testujte jednotlive funkcie

KONTAKT:
========

Ak sa problem stale vyskytuje, skontrolujte:
- Windows verziu (Windows 10/11)
- Antivirus nastavenia
- Prava administratora
- Spravnost cesty k suborom

Posledna aktualizacia: 2025-01-05
