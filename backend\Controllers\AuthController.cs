using Microsoft.AspNetCore.Mvc;
using SmartFireInspector.Api.Models.DTOs.Auth;
using SmartFireInspector.Api.Services;

namespace SmartFireInspector.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    [HttpPost("login")]
    public async Task<ActionResult<AuthResponse>> Login([FromBody] LoginRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new AuthResponse
                {
                    Success = false,
                    Message = "Neplatné údaje"
                });
            }

            _logger.LogInformation("Pokus o prihlásenie pre email: {Email}", request.Email);

            var result = await _authService.LoginAsync(request);

            if (!result.Success)
            {
                _logger.LogWarning("Neúspešné prihlásenie pre email: {Email}", request.Email);
                return Unauthorized(result);
            }

            _logger.LogInformation("Úspešné prihlásenie pre email: {Email}", request.Email);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba pri prihlasovaní pre email: {Email}", request.Email);
            return StatusCode(500, new AuthResponse
            {
                Success = false,
                Message = "Interná chyba servera"
            });
        }
    }

    [HttpGet("test-accounts")]
    public ActionResult<object> GetTestAccounts()
    {
        return Ok(new
        {
            message = "Testové kontá pre prihlásenie:",
            accounts = new[]
            {
                new { email = "<EMAIL>", password = "admin123", role = "Admin" },
                new { email = "<EMAIL>", password = "inspector123", role = "Inspector" },
                new { email = "<EMAIL>", password = "user123", role = "User" }
            }
        });
    }
}
