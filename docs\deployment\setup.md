# Návod na nastavenie projektu

## Požiadavky

### Backend
- .NET 8.0 SDK
- Visual Studio 2022 alebo VS Code

### Frontend
- Node.js 18+ 
- npm alebo yarn

### Databáza
- Supabase účet (pre produkciu)
- PostgreSQL 14+ (pre lokálny vývoj)

## Inštalácia

### 1. Klonovanie repozitára
```bash
git clone <repository-url>
cd SmartFireInspector
```

### 2. Backend setup
```bash
cd backend
dotnet restore
dotnet run
```

Backend bude dostupný na: `http://localhost:5000`

### 3. Frontend setup
```bash
cd web-app
npm install
npm run dev
```

Frontend bude dostupný na: `http://localhost:3000`

## Konfigurácia

### Backend (appsettings.json)
```json
{
  "JwtSettings": {
    "SecretKey": "your-secret-key-here",
    "Issuer": "SmartFireInspector",
    "Audience": "SmartFireInspector",
    "ExpiryInDays": 7
  },
  "Supabase": {
    "Url": "your-supabase-url",
    "Key": "your-supabase-anon-key",
    "ConnectionString": "your-postgresql-connection-string"
  }
}
```

### Frontend (constants.js)
```javascript
export const API_BASE_URL = 'http://localhost:5000/api';
```

## Testovanie

### Testové kontá
- **Admin**: <EMAIL> / admin123
- **Inspector**: <EMAIL> / inspector123
- **User**: <EMAIL> / user123

### API testovanie
- Swagger UI: `http://localhost:5000/swagger`
- Postman collection: `docs/api/SmartFireInspector.postman_collection.json`

## Deployment

### Backend
1. Publikovanie do Azure App Service
2. Nastavenie connection stringov
3. Konfigurácia CORS pre produkčnú doménu

### Frontend
1. Build: `npm run build`
2. Deploy do Netlify/Vercel
3. Nastavenie environment variables

### Databáza
1. Spustenie migrácií v Supabase
2. Vloženie seed dát
3. Nastavenie indexov pre výkon
